<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保洁管理 - 智慧后勤管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/cleaning.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="top-header">
        <div class="header-left">
            <button class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
            <h1 class="system-title">
                <i class="fas fa-building"></i>
                智慧后勤管理系统
            </h1>
        </div>
        <div class="header-right">
            <div class="notification-center">
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
            </div>
            <div class="user-profile">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="用户头像" class="user-avatar">
                <span class="user-name">张管理员</span>
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </header>

    <!-- 侧边导航栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-content">
            <!-- 用户信息卡片 -->
            <div class="user-card">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="用户头像" class="user-card-avatar">
                <div class="user-card-info">
                    <h3>张管理员</h3>
                    <p>系统管理员</p>
                </div>
            </div>

            <!-- 导航菜单 -->
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="procurement.html" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>
                        <span>采购管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="warehouse.html" class="nav-link">
                        <i class="fas fa-warehouse"></i>
                        <span>仓库管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="maintenance.html" class="nav-link">
                        <i class="fas fa-tools"></i>
                        <span>报修管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="engineering.html" class="nav-link">
                        <i class="fas fa-hard-hat"></i>
                        <span>工程管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="utilities.html" class="nav-link">
                        <i class="fas fa-bolt"></i>
                        <span>水电气管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="safety.html" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>安全生产管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="medical-waste.html" class="nav-link">
                        <i class="fas fa-biohazard"></i>
                        <span>医废管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="vehicle.html" class="nav-link">
                        <i class="fas fa-car"></i>
                        <span>用车管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="dormitory.html" class="nav-link">
                        <i class="fas fa-bed"></i>
                        <span>宿舍管理</span>
                    </a>
                </li>
                
                <li class="nav-item active">
                    <a href="cleaning.html" class="nav-link">
                        <i class="fas fa-broom"></i>
                        <span>保洁管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="inspection.html" class="nav-link">
                        <i class="fas fa-search"></i>
                        <span>巡检管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="contract.html" class="nav-link">
                        <i class="fas fa-file-contract"></i>
                        <span>合同管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="information.html" class="nav-link">
                        <i class="fas fa-bullhorn"></i>
                        <span>信息发布管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="property.html" class="nav-link">
                        <i class="fas fa-building"></i>
                        <span>物业管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="finance.html" class="nav-link">
                        <i class="fas fa-credit-card"></i>
                        <span>财务管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="catering.html" class="nav-link">
                        <i class="fas fa-utensils"></i>
                        <span>餐饮管理</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                <h2>
                    <i class="fas fa-broom"></i>
                    保洁管理
                </h2>
                <p>智能排班系统，清洁质量评估，耗材用量优化，消杀记录追溯，绿色清洁方案</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" id="newTaskBtn">
                    <i class="fas fa-plus"></i>
                    新建任务
                </button>
                <button class="btn btn-secondary" id="scheduleOptimizeBtn">
                    <i class="fas fa-calendar-alt"></i>
                    排班优化
                </button>
                <button class="btn btn-secondary" id="cleaningReportBtn">
                    <i class="fas fa-chart-bar"></i>
                    保洁报告
                </button>
            </div>
        </div>

        <!-- 保洁概览 -->
        <section class="cleaning-overview">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="card-content">
                        <h3>保洁人员</h3>
                        <div class="card-number">24人</div>
                        <div class="card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+2人</span>
                        </div>
                        <div class="card-detail">在岗人员: 22人</div>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="card-icon success">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="card-content">
                        <h3>今日任务</h3>
                        <div class="card-number">156项</div>
                        <div class="card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>完成率92%</span>
                        </div>
                        <div class="card-detail">剩余: 12项</div>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="card-icon warning">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="card-content">
                        <h3>质量评分</h3>
                        <div class="card-number">4.7分</div>
                        <div class="card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+0.3分</span>
                        </div>
                        <div class="card-detail">本周平均评分</div>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="card-icon info">
                        <i class="fas fa-spray-can"></i>
                    </div>
                    <div class="card-content">
                        <h3>耗材库存</h3>
                        <div class="card-number">85%</div>
                        <div class="card-trend down">
                            <i class="fas fa-arrow-down"></i>
                            <span>需补货3项</span>
                        </div>
                        <div class="card-detail">库存充足</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能模块导航 -->
        <section class="module-navigation">
            <div class="module-tabs">
                <button class="tab-btn active" data-tab="schedule">
                    <i class="fas fa-calendar-alt"></i>
                    智能排班
                </button>
                <button class="tab-btn" data-tab="quality">
                    <i class="fas fa-star"></i>
                    质量评估
                </button>
                <button class="tab-btn" data-tab="supplies">
                    <i class="fas fa-box"></i>
                    耗材管理
                </button>
                <button class="tab-btn" data-tab="disinfection">
                    <i class="fas fa-spray-can"></i>
                    消杀记录
                </button>
                <button class="tab-btn" data-tab="green">
                    <i class="fas fa-leaf"></i>
                    绿色清洁
                </button>
            </div>
        </section>

        <!-- 智能排班系统模块 -->
        <section class="tab-content active" id="schedule">
            <div class="schedule-dashboard">
                <div class="schedule-overview">
                    <h3>
                        <i class="fas fa-calendar-check"></i>
                        排班统计
                    </h3>
                    <div class="schedule-stats">
                        <div class="schedule-stat">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h4>今日排班</h4>
                                <div class="stat-number">22人</div>
                                <p>早班12人，晚班10人</p>
                            </div>
                        </div>
                        <div class="schedule-stat">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-content">
                                <h4>出勤率</h4>
                                <div class="stat-number">95.8%</div>
                                <p>1人请假</p>
                            </div>
                        </div>
                        <div class="schedule-stat">
                            <div class="stat-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="stat-content">
                                <h4>任务完成率</h4>
                                <div class="stat-number">92.3%</div>
                                <p>12项待完成</p>
                            </div>
                        </div>
                        <div class="schedule-stat">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h4>紧急任务</h4>
                                <div class="stat-number">3项</div>
                                <p>需要优先处理</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="task-assignment">
                    <div class="assignment-header">
                        <h3>
                            <i class="fas fa-brain"></i>
                            智能任务分配
                        </h3>
                        <div class="assignment-controls">
                            <select class="area-filter">
                                <option>全部区域</option>
                                <option>门诊楼</option>
                                <option>住院楼</option>
                                <option>办公楼</option>
                                <option>公共区域</option>
                            </select>
                            <select class="priority-filter">
                                <option>全部优先级</option>
                                <option>紧急</option>
                                <option>高</option>
                                <option>普通</option>
                                <option>低</option>
                            </select>
                            <button class="btn btn-primary" id="autoAssignBtn">
                                <i class="fas fa-magic"></i>
                                智能分配
                            </button>
                        </div>
                    </div>

                    <div class="task-grid">
                        <div class="task-card urgent">
                            <div class="task-header">
                                <h4>手术室深度清洁</h4>
                                <span class="priority-badge urgent">紧急</span>
                            </div>
                            <div class="task-info">
                                <div class="task-detail">
                                    <span class="detail-label">区域:</span>
                                    <span class="detail-value">手术室3号</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">预计时间:</span>
                                    <span class="detail-value">2小时</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">要求:</span>
                                    <span class="detail-value">无菌清洁、消毒</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">截止时间:</span>
                                    <span class="detail-value">16:00</span>
                                </div>
                            </div>
                            <div class="ai-recommendation">
                                <h5>
                                    <i class="fas fa-robot"></i>
                                    AI推荐
                                </h5>
                                <div class="recommendation-item">
                                    <div>推荐人员: 李阿姨 (专业清洁5年)</div>
                                    <div>匹配度: <span class="score high">95分</span></div>
                                    <div>原因: 手术室清洁经验丰富，当前空闲</div>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-sm btn-primary">立即分配</button>
                                <button class="btn btn-sm btn-secondary">查看详情</button>
                            </div>
                        </div>

                        <div class="task-card normal">
                            <div class="task-header">
                                <h4>门诊大厅日常清洁</h4>
                                <span class="priority-badge normal">普通</span>
                            </div>
                            <div class="task-info">
                                <div class="task-detail">
                                    <span class="detail-label">区域:</span>
                                    <span class="detail-value">门诊大厅</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">预计时间:</span>
                                    <span class="detail-value">1.5小时</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">要求:</span>
                                    <span class="detail-value">地面清洁、垃圾清理</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">截止时间:</span>
                                    <span class="detail-value">18:00</span>
                                </div>
                            </div>
                            <div class="ai-recommendation">
                                <h5>
                                    <i class="fas fa-robot"></i>
                                    AI推荐
                                </h5>
                                <div class="recommendation-item">
                                    <div>推荐人员: 王师傅 (工作效率高)</div>
                                    <div>匹配度: <span class="score medium">82分</span></div>
                                    <div>原因: 熟悉门诊区域，工作认真负责</div>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-sm btn-primary">分配任务</button>
                                <button class="btn btn-sm btn-secondary">修改任务</button>
                            </div>
                        </div>

                        <div class="task-card assigned">
                            <div class="task-header">
                                <h4>病房楼层清洁</h4>
                                <span class="priority-badge normal">普通</span>
                            </div>
                            <div class="task-info">
                                <div class="task-detail">
                                    <span class="detail-label">区域:</span>
                                    <span class="detail-value">5楼病房</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">执行人员:</span>
                                    <span class="detail-value">张阿姨</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">开始时间:</span>
                                    <span class="detail-value">14:30</span>
                                </div>
                                <div class="task-detail">
                                    <span class="detail-label">进度:</span>
                                    <span class="detail-value">60%</span>
                                </div>
                            </div>
                            <div class="task-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 60%"></div>
                                </div>
                                <span class="progress-text">进行中</span>
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-sm btn-info">联系人员</button>
                                <button class="btn btn-sm btn-secondary">查看进度</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="staff-schedule">
                    <div class="schedule-header">
                        <h3>
                            <i class="fas fa-users"></i>
                            人员排班表
                        </h3>
                        <div class="schedule-controls">
                            <input type="date" class="date-picker" value="2024-01-22">
                            <select class="shift-filter">
                                <option>全部班次</option>
                                <option>早班</option>
                                <option>晚班</option>
                                <option>夜班</option>
                            </select>
                            <button class="btn btn-secondary" id="exportScheduleBtn">
                                <i class="fas fa-download"></i>
                                导出排班表
                            </button>
                        </div>
                    </div>

                    <div class="schedule-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>姓名</th>
                                    <th>班次</th>
                                    <th>工作区域</th>
                                    <th>任务数量</th>
                                    <th>完成进度</th>
                                    <th>考勤状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="staff-info">
                                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" alt="员工头像" class="staff-avatar">
                                            <div>
                                                <strong>李阿姨</strong>
                                                <span>5年经验</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="shift-badge morning">早班</span>
                                    </td>
                                    <td>手术室、ICU</td>
                                    <td>5项</td>
                                    <td>
                                        <div class="progress-mini">
                                            <div class="progress-fill" style="width: 80%"></div>
                                        </div>
                                        <span>80%</span>
                                    </td>
                                    <td>
                                        <span class="attendance-badge present">在岗</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="staff-info">
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="员工头像" class="staff-avatar">
                                            <div>
                                                <strong>王师傅</strong>
                                                <span>3年经验</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="shift-badge morning">早班</span>
                                    </td>
                                    <td>门诊大厅</td>
                                    <td>3项</td>
                                    <td>
                                        <div class="progress-mini">
                                            <div class="progress-fill" style="width: 100%"></div>
                                        </div>
                                        <span>100%</span>
                                    </td>
                                    <td>
                                        <span class="attendance-badge present">在岗</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="staff-info">
                                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face" alt="员工头像" class="staff-avatar">
                                            <div>
                                                <strong>张阿姨</strong>
                                                <span>2年经验</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="shift-badge evening">晚班</span>
                                    </td>
                                    <td>病房楼</td>
                                    <td>4项</td>
                                    <td>
                                        <div class="progress-mini">
                                            <div class="progress-fill" style="width: 60%"></div>
                                        </div>
                                        <span>60%</span>
                                    </td>
                                    <td>
                                        <span class="attendance-badge present">在岗</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">查看详情</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="staff-info">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="员工头像" class="staff-avatar">
                                            <div>
                                                <strong>刘师傅</strong>
                                                <span>4年经验</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="shift-badge morning">早班</span>
                                    </td>
                                    <td>办公楼</td>
                                    <td>0项</td>
                                    <td>
                                        <div class="progress-mini">
                                            <div class="progress-fill" style="width: 0%"></div>
                                        </div>
                                        <span>0%</span>
                                    </td>
                                    <td>
                                        <span class="attendance-badge absent">请假</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="js/common.js"></script>
    <script src="js/cleaning.js"></script>
</body>
</html>
