<!-- 导航组件 - 可被其他页面引用 -->

<!-- 顶部导航栏 -->
<header class="top-header">
    <div class="header-left">
        <button class="menu-toggle" id="menuToggle">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="system-title">
            <i class="fas fa-building"></i>
            智慧后勤管理系统
        </h1>
    </div>
    <div class="header-right">
        <div class="notification-center">
            <button class="notification-btn">
                <i class="fas fa-bell"></i>
                <span class="notification-badge">3</span>
            </button>
        </div>
        <div class="user-profile">
            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="用户头像" class="user-avatar">
            <span class="user-name">张管理员</span>
            <i class="fas fa-chevron-down"></i>
        </div>
    </div>
</header>

<!-- 侧边导航栏 -->
<nav class="sidebar" id="sidebar">
    <div class="sidebar-content">
        <!-- 用户信息卡片 -->
        <div class="user-card">
            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="用户头像" class="user-card-avatar">
            <div class="user-card-info">
                <h3>张管理员</h3>
                <p>系统管理员</p>
            </div>
        </div>

        <!-- 导航菜单 -->
        <ul class="nav-menu">
            <li class="nav-item" data-page="index">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="procurement">
                <a href="procurement.html" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    <span>采购管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="warehouse">
                <a href="warehouse.html" class="nav-link">
                    <i class="fas fa-warehouse"></i>
                    <span>仓库管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="maintenance">
                <a href="maintenance.html" class="nav-link">
                    <i class="fas fa-tools"></i>
                    <span>报修管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="engineering">
                <a href="engineering.html" class="nav-link">
                    <i class="fas fa-hard-hat"></i>
                    <span>工程管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="utilities">
                <a href="utilities.html" class="nav-link">
                    <i class="fas fa-bolt"></i>
                    <span>水电气管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="safety">
                <a href="safety.html" class="nav-link">
                    <i class="fas fa-shield-alt"></i>
                    <span>安全生产管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="medical-waste">
                <a href="medical-waste.html" class="nav-link">
                    <i class="fas fa-biohazard"></i>
                    <span>医废管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="vehicle">
                <a href="vehicle.html" class="nav-link">
                    <i class="fas fa-car"></i>
                    <span>用车管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="dormitory">
                <a href="dormitory.html" class="nav-link">
                    <i class="fas fa-bed"></i>
                    <span>宿舍管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="cleaning">
                <a href="cleaning.html" class="nav-link">
                    <i class="fas fa-broom"></i>
                    <span>保洁管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="inspection">
                <a href="inspection.html" class="nav-link">
                    <i class="fas fa-search"></i>
                    <span>巡检管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="contract">
                <a href="contract.html" class="nav-link">
                    <i class="fas fa-file-contract"></i>
                    <span>合同管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="information">
                <a href="information.html" class="nav-link">
                    <i class="fas fa-bullhorn"></i>
                    <span>信息发布管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="property">
                <a href="property.html" class="nav-link">
                    <i class="fas fa-building"></i>
                    <span>物业管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="finance">
                <a href="finance.html" class="nav-link">
                    <i class="fas fa-credit-card"></i>
                    <span>财务管理</span>
                </a>
            </li>
            
            <li class="nav-item" data-page="catering">
                <a href="catering.html" class="nav-link">
                    <i class="fas fa-utensils"></i>
                    <span>餐饮管理</span>
                </a>
            </li>
        </ul>
    </div>
</nav>

<script>
// 导航组件JavaScript
(function() {
    // 设置当前页面的导航状态
    function setActiveNavigation() {
        const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === currentPage) {
                item.classList.add('active');
            }
        });
    }
    
    // 页面加载完成后设置导航状态
    document.addEventListener('DOMContentLoaded', setActiveNavigation);
})();
</script>
