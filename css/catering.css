/* 餐饮管理页面样式 */

/* 概览统计 */
.overview-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.card-trend.positive {
    color: #27ae60;
}

.card-trend.warning {
    color: #f39c12;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-content {
    padding: 20px;
    height: 300px;
}

.recent-activities {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.activities-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activities-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.activities-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-item.urgent {
    border-left: 4px solid #e74c3c;
}

.activity-item.normal {
    border-left: 4px solid #27ae60;
}

.activity-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.activity-item.urgent .activity-icon {
    background: #ffebee;
    color: #e74c3c;
}

.activity-item.normal .activity-icon {
    background: #e8f5e8;
    color: #27ae60;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.activity-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.activity-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    background: #f0f0f0;
    color: #666;
}

.tag.urgent {
    background: #ffebee;
    color: #c62828;
}

.tag.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.tag.info {
    background: #e3f2fd;
    color: #1976d2;
}

.activity-time {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
}

/* 采购与库存管理 */
.procurement-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.procurement-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.procurement-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.procurement-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.procurement-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.procurement-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.procurement-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.procurement-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.procurement-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.procurement-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.inventory-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.inventory-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.inventory-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.inventory-actions {
    display: flex;
    gap: 12px;
}

.inventory-filters {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.filter-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    min-width: 120px;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    flex: 1;
    max-width: 200px;
}

.inventory-table {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.item-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.item-image {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    object-fit: cover;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.badge-success {
    background: #e8f5e8;
    color: #2e7d32;
}

.badge-danger {
    background: #ffebee;
    color: #c62828;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

/* 供应商管理 */
.supplier-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.supplier-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.supplier-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.supplier-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.supplier-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.supplier-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.supplier-item.excellent {
    border-left: 4px solid #27ae60;
}

.supplier-item.good {
    border-left: 4px solid #3498db;
}

.supplier-info {
    flex: 1;
}

.supplier-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.supplier-info p {
    font-size: 13px;
    color: #666;
    margin-bottom: 10px;
}

.supplier-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.supplier-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 80px;
}

.rating-score {
    font-size: 18px;
    font-weight: 700;
    color: #333;
}

.rating-stars {
    color: #ffc107;
}

.supplier-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 菜品与营养管理 */
.menu-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.menu-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.menu-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.menu-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.menu-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.menu-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.menu-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.menu-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.menu-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.menu-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.menu-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.menu-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.menu-actions {
    display: flex;
    gap: 12px;
}

.menu-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.dish-card {
    border: 1px solid #f0f0f0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s;
    background: white;
}

.dish-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.dish-card.popular {
    border-left: 4px solid #e74c3c;
}

.dish-card.healthy {
    border-left: 4px solid #27ae60;
}

.dish-card.new {
    border-left: 4px solid #3498db;
}

.dish-image {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.dish-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.dish-card:hover .dish-image img {
    transform: scale(1.05);
}

.dish-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    color: white;
    background: #e74c3c;
}

.dish-badge.healthy {
    background: #27ae60;
}

.dish-badge.new {
    background: #3498db;
}

.dish-info {
    padding: 15px;
}

.dish-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.dish-description {
    font-size: 13px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.4;
}

.dish-nutrition {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
}

.nutrition-item {
    font-size: 11px;
    color: #999;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
}

.dish-price {
    font-size: 18px;
    font-weight: 700;
    color: #e74c3c;
    margin-bottom: 15px;
}

.dish-actions {
    display: flex;
    gap: 8px;
}

.nutrition-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.nutrition-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.nutrition-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nutrition-content {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.nutrition-analysis h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.nutrition-chart {
    height: 300px;
}

.health-recommendations h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.recommendation-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recommendation-item {
    display: flex;
    gap: 12px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s;
}

.recommendation-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.recommendation-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.recommendation-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.recommendation-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .overview-stats,
    .procurement-stats,
    .menu-stats {
        grid-template-columns: 1fr;
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .menu-grid {
        grid-template-columns: 1fr;
    }

    .nutrition-content {
        grid-template-columns: 1fr;
    }

    .supplier-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .supplier-rating {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .supplier-actions {
        flex-direction: row;
        justify-content: center;
    }

    .inventory-actions,
    .menu-actions {
        flex-direction: column;
    }

    .action-buttons {
        flex-direction: column;
    }
}
