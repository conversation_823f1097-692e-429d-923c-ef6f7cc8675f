/* 保洁管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 保洁概览 */
.cleaning-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    flex-shrink: 0;
}

.card-icon.success {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.card-icon.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.card-icon.info {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.card-trend.up {
    color: #27ae60;
}

.card-trend.down {
    color: #e74c3c;
}

.card-trend.stable {
    color: #95a5a6;
}

.card-detail {
    font-size: 13px;
    color: #666;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.tab-btn.active {
    background: #27ae60;
    color: white;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 智能排班系统 */
.schedule-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.schedule-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.schedule-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.schedule-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.schedule-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.schedule-stat:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.schedule-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.schedule-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.schedule-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.schedule-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

/* 任务分配 */
.task-assignment {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.assignment-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.assignment-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.assignment-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.area-filter,
.priority-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.task-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.task-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.task-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-card.urgent {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.task-card.normal {
    border-left: 4px solid #3498db;
}

.task-card.assigned {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.3) 100%);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.task-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.priority-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.priority-badge.urgent {
    background: #ffebee;
    color: #c62828;
}

.priority-badge.normal {
    background: #e3f2fd;
    color: #1976d2;
}

.task-info {
    margin-bottom: 15px;
}

.task-detail {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    font-size: 13px;
    border-bottom: 1px solid #f5f5f5;
}

.task-detail:last-child {
    border-bottom: none;
}

.detail-label {
    color: #666;
}

.detail-value {
    font-weight: 600;
    color: #333;
}

.ai-recommendation {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.ai-recommendation h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.recommendation-item {
    font-size: 13px;
}

.recommendation-item > div {
    margin-bottom: 6px;
}

.score {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.score.high {
    background: #e8f5e8;
    color: #2e7d32;
}

.score.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.task-progress {
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-radius: 4px;
    transition: width 0.3s;
}

.progress-text {
    font-size: 12px;
    color: #666;
}

.task-actions {
    display: flex;
    gap: 8px;
}

/* 人员排班表 */
.staff-schedule {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.schedule-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.schedule-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.schedule-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.date-picker,
.shift-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.schedule-table {
    overflow-x: auto;
}

.schedule-table table {
    width: 100%;
    border-collapse: collapse;
}

.schedule-table th,
.schedule-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.schedule-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.staff-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.staff-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.staff-info > div {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.staff-info strong {
    font-size: 14px;
    color: #333;
}

.staff-info span {
    font-size: 12px;
    color: #666;
}

.shift-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.shift-badge.morning {
    background: #e3f2fd;
    color: #1976d2;
}

.shift-badge.evening {
    background: #fff3e0;
    color: #ef6c00;
}

.shift-badge.night {
    background: #f3e5f5;
    color: #7b1fa2;
}

.progress-mini {
    width: 60px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    display: inline-block;
    margin-right: 8px;
}

.attendance-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.attendance-badge.present {
    background: #e8f5e8;
    color: #2e7d32;
}

.attendance-badge.absent {
    background: #ffebee;
    color: #c62828;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .schedule-stats {
        grid-template-columns: 1fr;
    }
    
    .assignment-header,
    .schedule-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .assignment-controls,
    .schedule-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .task-grid {
        grid-template-columns: 1fr;
    }
    
    .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .task-actions {
        justify-content: center;
    }
    
    .staff-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
