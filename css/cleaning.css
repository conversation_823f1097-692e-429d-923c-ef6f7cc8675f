/* 保洁管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 保洁概览 */
.cleaning-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    flex-shrink: 0;
}

.card-icon.success {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.card-icon.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.card-icon.info {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.card-trend.up {
    color: #27ae60;
}

.card-trend.down {
    color: #e74c3c;
}

.card-trend.stable {
    color: #95a5a6;
}

.card-detail {
    font-size: 13px;
    color: #666;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.tab-btn.active {
    background: #27ae60;
    color: white;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 智能排班系统 */
.schedule-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.schedule-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.schedule-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.schedule-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.schedule-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.schedule-stat:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.schedule-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.schedule-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.schedule-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.schedule-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

/* 任务分配 */
.task-assignment {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.assignment-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.assignment-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.assignment-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.area-filter,
.priority-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.task-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.task-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.task-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-card.urgent {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.task-card.normal {
    border-left: 4px solid #3498db;
}

.task-card.assigned {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.3) 100%);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.task-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.priority-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.priority-badge.urgent {
    background: #ffebee;
    color: #c62828;
}

.priority-badge.normal {
    background: #e3f2fd;
    color: #1976d2;
}

.task-info {
    margin-bottom: 15px;
}

.task-detail {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    font-size: 13px;
    border-bottom: 1px solid #f5f5f5;
}

.task-detail:last-child {
    border-bottom: none;
}

.detail-label {
    color: #666;
}

.detail-value {
    font-weight: 600;
    color: #333;
}

.ai-recommendation {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.ai-recommendation h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.recommendation-item {
    font-size: 13px;
}

.recommendation-item > div {
    margin-bottom: 6px;
}

.score {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.score.high {
    background: #e8f5e8;
    color: #2e7d32;
}

.score.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.task-progress {
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-radius: 4px;
    transition: width 0.3s;
}

.progress-text {
    font-size: 12px;
    color: #666;
}

.task-actions {
    display: flex;
    gap: 8px;
}

/* 人员排班表 */
.staff-schedule {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.schedule-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.schedule-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.schedule-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.date-picker,
.shift-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.schedule-table {
    overflow-x: auto;
}

.schedule-table table {
    width: 100%;
    border-collapse: collapse;
}

.schedule-table th,
.schedule-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.schedule-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.staff-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.staff-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.staff-info > div {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.staff-info strong {
    font-size: 14px;
    color: #333;
}

.staff-info span {
    font-size: 12px;
    color: #666;
}

.shift-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.shift-badge.morning {
    background: #e3f2fd;
    color: #1976d2;
}

.shift-badge.evening {
    background: #fff3e0;
    color: #ef6c00;
}

.shift-badge.night {
    background: #f3e5f5;
    color: #7b1fa2;
}

.progress-mini {
    width: 60px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    display: inline-block;
    margin-right: 8px;
}

.attendance-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.attendance-badge.present {
    background: #e8f5e8;
    color: #2e7d32;
}

.attendance-badge.absent {
    background: #ffebee;
    color: #c62828;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .schedule-stats {
        grid-template-columns: 1fr;
    }
    
    .assignment-header,
    .schedule-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .assignment-controls,
    .schedule-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .task-grid {
        grid-template-columns: 1fr;
    }
    
    .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .task-actions {
        justify-content: center;
    }
    
    .staff-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* 清洁质量评估 */
.quality-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.quality-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.quality-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quality-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.quality-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.quality-stat:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.quality-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.quality-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.quality-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.quality-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.inspection-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.inspection-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.inspection-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.inspection-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.inspector-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.inspection-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.inspection-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.inspection-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.inspection-item.excellent {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.3) 100%);
}

.inspection-item.good {
    border-left: 4px solid #3498db;
}

.inspection-info {
    flex: 1;
}

.inspection-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.inspection-info > p {
    font-size: 13px;
    color: #666;
    margin-bottom: 15px;
}

.inspection-details {
    margin-bottom: 15px;
}

.inspection-criteria {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.criteria-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
}

.criteria-name {
    min-width: 80px;
    color: #666;
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars i {
    color: #f39c12;
    font-size: 12px;
}

.rating-score {
    font-weight: 600;
    color: #333;
    margin-left: 8px;
}

.inspection-notes {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.inspection-notes p {
    font-size: 13px;
    color: #333;
    margin: 0;
    line-height: 1.4;
}

.inspection-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    margin: 0 15px;
}

.score-number {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 4px;
}

.score-label {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
}

.inspection-score.excellent .score-number {
    color: #27ae60;
}

.inspection-score.excellent .score-label {
    background: #e8f5e8;
    color: #2e7d32;
}

.inspection-score.good .score-number {
    color: #3498db;
}

.inspection-score.good .score-label {
    background: #e3f2fd;
    color: #1976d2;
}

.inspection-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

.user-feedback {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.feedback-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.feedback-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.feedback-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.rating-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.feedback-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.feedback-item {
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.feedback-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.feedback-item.positive {
    border-left: 4px solid #27ae60;
}

.feedback-item.neutral {
    border-left: 4px solid #f39c12;
}

.feedback-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info > div {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-info strong {
    font-size: 14px;
    color: #333;
}

.user-info span {
    font-size: 12px;
    color: #666;
}

.feedback-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rating-text {
    font-weight: 600;
    color: #333;
}

.feedback-content p {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 10px;
    font-style: italic;
}

.feedback-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
}

/* 耗材用量优化 */
.supplies-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.supplies-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.supplies-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.supplies-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.supply-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.supply-stat:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.supply-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.supply-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.supply-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.supply-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.inventory-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.inventory-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.inventory-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.inventory-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.category-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.inventory-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.supply-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.supply-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.supply-card.normal {
    border-left: 4px solid #27ae60;
}

.supply-card.warning {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.supply-card.danger {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.supply-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.supply-image {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.supply-info {
    flex: 1;
}

.supply-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.supply-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.supply-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.supply-status.normal {
    background: #e8f5e8;
    color: #2e7d32;
}

.supply-status.warning {
    background: #fff3e0;
    color: #ef6c00;
}

.supply-status.danger {
    background: #ffebee;
    color: #c62828;
}

.supply-details {
    margin-bottom: 15px;
}

.detail-value.warning {
    color: #ef6c00;
    font-weight: 600;
}

.detail-value.danger {
    color: #c62828;
    font-weight: 600;
}

.detail-value.success {
    color: #2e7d32;
    font-weight: 600;
}

.usage-trend {
    height: 100px;
    margin-bottom: 15px;
}

.low-stock-alert,
.out-of-stock-alert {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    border-radius: 6px;
    font-size: 13px;
    margin-bottom: 15px;
}

.low-stock-alert {
    background: #fff3e0;
    color: #ef6c00;
}

.out-of-stock-alert {
    background: #ffebee;
    color: #c62828;
}

.supply-actions {
    display: flex;
    gap: 8px;
}

/* 消杀记录追溯 */
.disinfection-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.disinfection-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.disinfection-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.disinfection-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.disinfection-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.disinfection-stat:hover {
    border-color: #e74c3c;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.1);
}

.disinfection-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.disinfection-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.disinfection-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.disinfection-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.disinfection-records {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.records-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.records-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.records-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.records-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.record-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.record-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.record-item.completed {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.3) 100%);
}

.record-item.in-progress {
    border-left: 4px solid #3498db;
    background: linear-gradient(135deg, #e3f2fd 0%, rgba(227, 242, 253, 0.3) 100%);
}

.record-info {
    flex: 1;
}

.record-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.record-info > p {
    font-size: 13px;
    color: #666;
    margin-bottom: 15px;
}

.record-details {
    margin-bottom: 15px;
}

.compliance-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.compliance-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #27ae60;
}

.compliance-item i {
    color: #27ae60;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    transition: width 0.3s;
}

.progress-text {
    font-size: 13px;
    font-weight: 600;
    color: #3498db;
}

.record-status {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    margin: 0 15px;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    align-self: flex-start;
}

.record-status.completed {
    background: #e8f5e8;
    color: #2e7d32;
}

.record-status.in-progress {
    background: #e3f2fd;
    color: #1976d2;
}

.record-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 绿色清洁方案 */
.green-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.green-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.green-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.green-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.green-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.green-stat:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.green-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.green-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.green-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.green-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.eco-products {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.products-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.products-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.products-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.certification-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.products-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.product-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.product-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-card.certified {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.3) 100%);
}

.product-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.product-image {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.product-info {
    flex: 1;
}

.product-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.product-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.certification-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.cert-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.cert-badge.leed {
    background: #e8f5e8;
    color: #2e7d32;
}

.cert-badge.green {
    background: #e3f2fd;
    color: #1976d2;
}

.cert-badge.organic {
    background: #fff3e0;
    color: #ef6c00;
}

.product-benefits {
    margin-bottom: 15px;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    color: #333;
}

.benefit-item i {
    color: #27ae60;
    font-size: 12px;
}

.environmental-impact {
    margin-bottom: 15px;
}

.environmental-impact h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.impact-metrics {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.metric {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
}

.metric-label {
    color: #666;
}

.metric-value {
    font-weight: 600;
}

.metric-value.low {
    color: #27ae60;
}

.metric-value.none {
    color: #27ae60;
}

.metric-value.high {
    color: #3498db;
}

.metric-value.yes {
    color: #27ae60;
}

.product-actions {
    display: flex;
    gap: 8px;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .quality-stats,
    .supplies-stats,
    .disinfection-stats,
    .green-stats {
        grid-template-columns: 1fr;
    }

    .inspection-header,
    .feedback-header,
    .inventory-header,
    .records-header,
    .products-header {
        flex-direction: column;
        gap: 15px;
    }

    .inspection-controls,
    .feedback-controls,
    .inventory-controls,
    .records-controls,
    .products-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .inventory-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }

    .inspection-item,
    .feedback-item,
    .supply-card,
    .record-item,
    .product-card {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .inspection-actions,
    .supply-actions,
    .record-actions,
    .product-actions {
        flex-direction: row;
        justify-content: center;
    }

    .inspection-score,
    .record-status {
        margin: 0;
        align-self: flex-start;
    }
}
