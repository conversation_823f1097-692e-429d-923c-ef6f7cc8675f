/* 通用样式 - 智慧后勤管理系统 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* 顶部导航栏 */
.top-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.menu-toggle:hover {
    background-color: rgba(255,255,255,0.1);
}

.system-title {
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.notification-center {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
    position: relative;
}

.notification-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 20px;
    transition: background-color 0.3s;
}

.user-profile:hover {
    background-color: rgba(255,255,255,0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
}

/* 侧边导航栏 */
.sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    width: 260px;
    height: calc(100vh - 60px);
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    overflow-y: auto;
    transition: transform 0.3s ease;
    z-index: 999;
}

.sidebar.collapsed {
    transform: translateX(-260px);
}

.sidebar-content {
    padding: 20px 0;
}

.user-card {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.user-card-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 12px;
}

.user-card-info h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.user-card-info p {
    font-size: 12px;
    color: #666;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: #f8f9fa;
    color: #667eea;
}

.nav-item.active .nav-link {
    background-color: #e8f2ff;
    color: #667eea;
    border-left-color: #667eea;
}

.nav-link i {
    width: 20px;
    margin-right: 12px;
    font-size: 16px;
}

.nav-link span {
    font-size: 14px;
    font-weight: 500;
}

/* 主内容区域 */
.main-content {
    margin-left: 260px;
    margin-top: 60px;
    padding: 30px;
    min-height: calc(100vh - 60px);
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-260px);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 20px 15px;
    }
    
    .system-title {
        font-size: 16px;
    }
    
    .user-name {
        display: none;
    }
}

/* 通用按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 20px;
    margin-bottom: 20px;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
