/* 合同管理页面样式 */

/* 概览统计 */
.overview-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.card-trend.positive {
    color: #27ae60;
}

.card-trend.negative {
    color: #e74c3c;
}

.card-trend.warning {
    color: #f39c12;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-content {
    padding: 20px;
    height: 300px;
}

.recent-contracts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.contracts-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contracts-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.contracts-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contract-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
}

.contract-item:hover {
    background: #f8f9fa;
}

.contract-item.pending {
    border-left: 4px solid #f39c12;
}

.contract-item.signed {
    border-left: 4px solid #27ae60;
}

.contract-item.warning {
    border-left: 4px solid #e74c3c;
}

.contract-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.contract-item.pending .contract-icon {
    background: #fff3e0;
    color: #f39c12;
}

.contract-item.signed .contract-icon {
    background: #e8f5e8;
    color: #27ae60;
}

.contract-item.warning .contract-icon {
    background: #ffebee;
    color: #e74c3c;
}

.contract-content {
    flex: 1;
}

.contract-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.contract-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.contract-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    background: #f0f0f0;
    color: #666;
}

.tag.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.tag.pending {
    background: #fff3e0;
    color: #ef6c00;
}

.tag.warning {
    background: #ffebee;
    color: #c62828;
}

.tag.urgent {
    background: #ffebee;
    color: #c62828;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.contract-amount {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-right: 15px;
}

.contract-time {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
}

/* 智能条款审查 */
.clause-review-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.review-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.review-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.review-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.review-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.review-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.review-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.review-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.review-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.review-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.ai-review-tool {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.tool-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tool-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tool-actions {
    display: flex;
    gap: 12px;
}

.review-workspace {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.contract-input {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.input-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.input-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.contract-template {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.text-editor {
    flex: 1;
}

.text-editor textarea {
    width: 100%;
    height: 400px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
}

.review-results {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.results-summary {
    display: flex;
    gap: 12px;
}

.risk-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.risk-level.high {
    background: #ffebee;
    color: #c62828;
}

.risk-level.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.risk-level.low {
    background: #e3f2fd;
    color: #1976d2;
}

.risk-items {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.risk-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
}

.risk-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.risk-item.high-risk {
    border-left: 4px solid #e74c3c;
}

.risk-item.medium-risk {
    border-left: 4px solid #f39c12;
}

.risk-header {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    gap: 12px;
}

.risk-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.risk-item.high-risk .risk-icon {
    background: #ffebee;
    color: #e74c3c;
}

.risk-item.medium-risk .risk-icon {
    background: #fff3e0;
    color: #f39c12;
}

.risk-info {
    flex: 1;
}

.risk-info h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.risk-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.risk-level-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.risk-level-badge.high {
    background: #ffebee;
    color: #c62828;
}

.risk-level-badge.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.risk-content {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.original-clause,
.risk-analysis,
.suggested-clause {
    padding: 12px;
    border-radius: 6px;
}

.original-clause {
    background: #f8f9fa;
    border-left: 3px solid #ddd;
}

.risk-analysis {
    background: #fff3e0;
    border-left: 3px solid #f39c12;
}

.suggested-clause {
    background: #e8f5e8;
    border-left: 3px solid #27ae60;
}

.original-clause h6,
.risk-analysis h6,
.suggested-clause h6 {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.original-clause p,
.suggested-clause p {
    font-size: 13px;
    color: #333;
    margin: 0;
    font-style: italic;
}

.risk-analysis ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.risk-analysis li {
    font-size: 13px;
    color: #333;
    margin-bottom: 4px;
    padding-left: 15px;
    position: relative;
}

.risk-analysis li:before {
    content: "•";
    color: #f39c12;
    position: absolute;
    left: 0;
}

.risk-actions {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    gap: 8px;
}

.template-comparison {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.comparison-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comparison-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.comparison-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.industry-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.comparison-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.comparison-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
}

.comparison-title {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comparison-title h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.missing-count,
.suggestion-count {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: #e3f2fd;
    color: #1976d2;
}

.missing-clauses,
.standardization-suggestions {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.missing-item,
.suggestion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    transition: all 0.3s;
}

.missing-item:hover,
.suggestion-item:hover {
    background: #f8f9fa;
}

.missing-item i {
    color: #27ae60;
    font-size: 16px;
}

.missing-item span {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.suggestion-content {
    flex: 1;
}

.suggestion-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.suggestion-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

/* 履约进度监控 */
.performance-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.performance-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.performance-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.performance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.performance-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.performance-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.performance-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.performance-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.performance-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.performance-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.contract-timeline {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.timeline-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.timeline-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.timeline-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.time-range,
.contract-status {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.timeline-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.timeline-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
}

.timeline-item:hover {
    background: #f8f9fa;
}

.timeline-item.urgent {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.timeline-item.normal {
    border-left: 4px solid #27ae60;
}

.timeline-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 60px;
}

.date-day {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    line-height: 1;
}

.date-month {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.timeline-content-item {
    flex: 1;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.timeline-content-item .timeline-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.timeline-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.timeline-status.urgent {
    background: #ffebee;
    color: #c62828;
}

.timeline-status.normal {
    background: #e8f5e8;
    color: #2e7d32;
}

.timeline-content-item p {
    font-size: 13px;
    color: #666;
    margin-bottom: 15px;
}

.timeline-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
}

.detail-label {
    color: #666;
    min-width: 80px;
}

.detail-value {
    color: #333;
    font-weight: 500;
}

.detail-value.high {
    color: #e74c3c;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin: 0 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s;
}

.progress-text {
    font-size: 12px;
    font-weight: 600;
    color: #667eea;
}

.rating-stars {
    display: flex;
    gap: 2px;
    margin-right: 8px;
}

.rating-stars i {
    color: #ffc107;
    font-size: 12px;
}

.rating-text {
    font-size: 12px;
    color: #666;
}

.timeline-actions {
    display: flex;
    gap: 8px;
}

/* 风险预警提示 */
.risk-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.risk-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.risk-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.risk-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.risk-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.risk-stat:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.risk-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.risk-stat.urgent .stat-icon {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.risk-stat.high .stat-icon {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.risk-stat.medium .stat-icon {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.risk-stat.low .stat-icon {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.risk-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.risk-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.risk-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.risk-alerts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.alerts-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alerts-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alerts-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.risk-type-filter,
.risk-level-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.alerts-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.alert-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
}

.alert-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-item.urgent {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.alert-item.high {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.alert-item.medium {
    border-left: 4px solid #3498db;
    background: linear-gradient(135deg, #e3f2fd 0%, rgba(227, 242, 253, 0.3) 100%);
}

.alert-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.alert-item.urgent .alert-icon {
    background: #ffebee;
    color: #e74c3c;
}

.alert-item.high .alert-icon {
    background: #fff3e0;
    color: #f39c12;
}

.alert-item.medium .alert-icon {
    background: #e3f2fd;
    color: #3498db;
}

.alert-content {
    flex: 1;
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.alert-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.alert-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.alert-level.urgent {
    background: #ffebee;
    color: #c62828;
}

.alert-level.high {
    background: #fff3e0;
    color: #ef6c00;
}

.alert-level.medium {
    background: #e3f2fd;
    color: #1976d2;
}

.alert-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.5;
}

.alert-details {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 15px;
}

.alert-actions {
    display: flex;
    gap: 8px;
    align-self: flex-start;
}

/* 电子签章集成 */
.signature-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.signature-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.signature-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.signature-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.signature-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.signature-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.signature-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.signature-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.signature-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.signature-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.signature-workspace {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.workspace-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.workspace-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.workspace-actions {
    display: flex;
    gap: 12px;
}

.pending-signatures {
    padding: 20px;
}

.pending-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.pending-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.pending-count {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: #e3f2fd;
    color: #1976d2;
}

.signature-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.signature-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.signature-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.signature-item.urgent {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.signature-item.normal {
    border-left: 4px solid #27ae60;
}

.signature-info {
    flex: 1;
    display: flex;
    gap: 15px;
}

.contract-preview {
    width: 50px;
    height: 60px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #e74c3c;
    flex-shrink: 0;
}

.contract-details {
    flex: 1;
}

.contract-details h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.contract-details > p {
    font-size: 13px;
    color: #666;
    margin-bottom: 12px;
}

.signature-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.signer-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.signer-item.completed {
    color: #27ae60;
}

.signer-item.pending {
    color: #f39c12;
}

.signer-item.waiting {
    color: #999;
}

.signer-item i {
    font-size: 12px;
}

.signature-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

.signature-security {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.security-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.security-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.security-status.online {
    color: #27ae60;
}

.security-content {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.cert-management,
.signature-log {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.cert-management h4,
.signature-log h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.cert-list,
.log-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.cert-item,
.log-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.cert-item:hover,
.log-item:hover {
    background: #f8f9fa;
}

.cert-info,
.log-content {
    flex: 1;
}

.cert-info h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.cert-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.cert-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.cert-status.valid {
    background: #e8f5e8;
    color: #2e7d32;
}

.cert-status.warning {
    background: #fff3e0;
    color: #ef6c00;
}

.cert-actions {
    display: flex;
    gap: 8px;
}

.log-time {
    font-size: 12px;
    color: #999;
    min-width: 100px;
}

.log-content {
    font-size: 13px;
    color: #333;
}

.log-user {
    font-weight: 600;
}

.log-action {
    color: #666;
}

.log-contract {
    font-weight: 500;
    color: #667eea;
}

.log-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}

.log-status.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.log-status.info {
    background: #e3f2fd;
    color: #1976d2;
}

/* 法律合规检测 */
.compliance-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.compliance-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.compliance-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.compliance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.compliance-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.compliance-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.compliance-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.compliance-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.compliance-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.compliance-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.compliance-checker {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.checker-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checker-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.checker-actions {
    display: flex;
    gap: 12px;
}

.compliance-results {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.results-summary {
    display: flex;
    gap: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.summary-item.compliant {
    color: #27ae60;
}

.summary-item.warning {
    color: #f39c12;
}

.summary-item.violation {
    color: #e74c3c;
}

.compliance-issues {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.issue-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
}

.issue-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.issue-item.high-risk {
    border-left: 4px solid #e74c3c;
}

.issue-item.medium-risk {
    border-left: 4px solid #f39c12;
}

.issue-header {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    gap: 12px;
}

.issue-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.issue-item.high-risk .issue-icon {
    background: #ffebee;
    color: #e74c3c;
}

.issue-item.medium-risk .issue-icon {
    background: #fff3e0;
    color: #f39c12;
}

.issue-info {
    flex: 1;
}

.issue-info h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.issue-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.issue-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.issue-level.high {
    background: #ffebee;
    color: #c62828;
}

.issue-level.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.issue-content {
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.legal-reference,
.standard-reference,
.violation-analysis,
.compliance-suggestion {
    padding: 12px;
    border-radius: 6px;
}

.legal-reference,
.standard-reference {
    background: #f8f9fa;
    border-left: 3px solid #ddd;
}

.violation-analysis {
    background: #fff3e0;
    border-left: 3px solid #f39c12;
}

.compliance-suggestion {
    background: #e8f5e8;
    border-left: 3px solid #27ae60;
}

.legal-reference h6,
.standard-reference h6,
.violation-analysis h6,
.compliance-suggestion h6 {
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.legal-reference p,
.standard-reference p,
.compliance-suggestion p {
    font-size: 13px;
    color: #333;
    margin: 0;
    line-height: 1.5;
}

.violation-analysis ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.violation-analysis li {
    font-size: 13px;
    color: #333;
    margin-bottom: 4px;
    padding-left: 15px;
    position: relative;
}

.violation-analysis li:before {
    content: "•";
    color: #f39c12;
    position: absolute;
    left: 0;
}

.issue-actions {
    padding: 15px;
    background: #f8f9fa;
    display: flex;
    gap: 8px;
}

.regulation-updates {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.updates-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.updates-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.updates-filter {
    display: flex;
    gap: 12px;
    align-items: center;
}

.regulation-type {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.updates-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.update-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.update-item:hover {
    background: #f8f9fa;
}

.update-item.new {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, rgba(248, 249, 255, 0.3) 100%);
}

.update-date {
    font-size: 12px;
    color: #999;
    min-width: 80px;
    font-weight: 600;
}

.update-content {
    flex: 1;
}

.update-content h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.update-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    line-height: 1.5;
}

.update-impact {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.impact-label {
    color: #666;
}

.impact-value {
    font-weight: 600;
    color: #667eea;
}

.update-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .review-workspace {
        grid-template-columns: 1fr;
    }

    .security-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .overview-stats,
    .review-stats,
    .performance-stats,
    .risk-stats,
    .signature-stats,
    .compliance-stats {
        grid-template-columns: 1fr;
    }

    .contracts-header,
    .tool-header,
    .comparison-header,
    .timeline-header,
    .alerts-header,
    .workspace-header,
    .security-header,
    .checker-header,
    .updates-header {
        flex-direction: column;
        gap: 15px;
    }

    .tool-actions,
    .comparison-controls,
    .timeline-controls,
    .alerts-controls,
    .workspace-actions,
    .checker-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .contract-item,
    .timeline-item,
    .alert-item,
    .signature-item,
    .update-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .contract-amount,
    .contract-time,
    .alert-actions,
    .signature-actions,
    .update-actions {
        align-self: stretch;
        justify-content: center;
    }

    .timeline-date {
        flex-direction: row;
        justify-content: center;
        gap: 5px;
        min-width: auto;
    }

    .results-summary {
        flex-direction: column;
        gap: 10px;
    }
}
