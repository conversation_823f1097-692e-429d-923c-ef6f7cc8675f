/* 工程管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 工程概览 */
.engineering-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    flex-shrink: 0;
}

.card-icon.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.card-trend.up {
    color: #27ae60;
}

.card-trend.down {
    color: #e74c3c;
}

.card-trend.stable {
    color: #95a5a6;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 项目监管 */
.project-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.projects-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.projects-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.project-card {
    border: 1px solid #f0f0f0;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s;
}

.project-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.project-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.project-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.project-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.project-status.on-schedule {
    background: #d4edda;
    color: #155724;
}

.project-status.delayed {
    background: #f8d7da;
    color: #721c24;
}

.project-progress {
    margin-bottom: 15px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
    transition: width 0.3s;
}

.progress-fill.delayed {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.progress-details {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

.project-milestones {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.milestone {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.milestone.completed {
    background: #d4edda;
    color: #155724;
}

.milestone.current {
    background: #fff3cd;
    color: #856404;
}

.milestone.pending {
    background: #f8f9fa;
    color: #6c757d;
}

.project-actions {
    display: flex;
    gap: 8px;
}

/* 甘特图 */
.gantt-chart {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.gantt-chart h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* 质量评估 */
.quality-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.quality-metrics {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.quality-metrics h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.metric-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.metric-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.metric-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.metric-value {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 6px;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.metric-trend.up {
    color: #27ae60;
}

.metric-trend.down {
    color: #e74c3c;
}

.metric-trend.stable {
    color: #95a5a6;
}

/* 质量评估记录 */
.quality-assessments {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.assessments-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.assessments-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.assessments-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.assessment-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.assessment-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.assessment-info {
    flex: 1;
}

.assessment-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
}

.assessment-info p {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.assessment-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
}

.assessment-score {
    text-align: center;
    min-width: 80px;
}

.score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin: 0 auto 8px;
    position: relative;
}

.score-value {
    font-size: 18px;
    font-weight: 700;
}

.score-max {
    font-size: 12px;
    opacity: 0.8;
}

.score-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.score-status.excellent {
    background: #d4edda;
    color: #155724;
}

.score-status.good {
    background: #d1ecf1;
    color: #0c5460;
}

.assessment-actions {
    display: flex;
    gap: 8px;
}

/* 按钮样式 */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .projects-grid,
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .project-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .assessment-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .assessment-actions {
        justify-content: center;
    }
}

/* 隐蔽工程 */
.hidden-works {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.works-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.works-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.works-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.work-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.work-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.work-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.work-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.work-info {
    flex: 1;
}

.work-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.work-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.work-qr {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #667eea;
    font-weight: 500;
}

.work-details {
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.detail-value {
    font-size: 14px;
    color: #333;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status-badge.passed {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.work-actions {
    display: flex;
    gap: 8px;
}

/* 工程档案 */
.archives-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.archives-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.archives-browser {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.browser-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.browser-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.browser-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.file-tree {
    padding: 20px;
}

.folder-item {
    margin-bottom: 20px;
}

.folder-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.folder-header:hover {
    background: #e9ecef;
}

.folder-header span:first-of-type {
    flex: 1;
    font-weight: 600;
    color: #333;
}

.file-count {
    font-size: 12px;
    color: #666;
}

.folder-content {
    margin-top: 10px;
    padding-left: 25px;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 15px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: all 0.3s;
}

.file-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.file-info {
    flex: 1;
}

.file-info h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.file-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.file-actions {
    display: flex;
    gap: 8px;
}

/* 风险预警 */
.risk-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.risk-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.risk-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.risk-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.risk-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
}

.risk-card.high {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border: 1px solid #f8bbd9;
}

.risk-card.medium {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border: 1px solid #ffcc80;
}

.risk-card.low {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border: 1px solid #a5d6a7;
}

.risk-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.risk-card.high .risk-icon {
    background: #e74c3c;
}

.risk-card.medium .risk-icon {
    background: #f39c12;
}

.risk-card.low .risk-icon {
    background: #27ae60;
}

.risk-content h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
}

.risk-count {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.risk-content p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.risk-alerts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.alerts-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alerts-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alerts-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.alert-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s;
}

.alert-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-item.high-risk {
    border-left: 4px solid #e74c3c;
}

.alert-item.medium-risk {
    border-left: 4px solid #f39c12;
}

.alert-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 4px;
}

.alert-item.high-risk .alert-indicator {
    background: #e74c3c;
}

.alert-item.medium-risk .alert-indicator {
    background: #f39c12;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.alert-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.5;
}

.alert-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 12px;
}

.risk-level {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.alert-item.high-risk .risk-level {
    background: #ffebee;
    color: #c62828;
}

.alert-item.medium-risk .risk-level {
    background: #fff3e0;
    color: #ef6c00;
}

.risk-category {
    background: #f8f9fa;
    color: #667eea;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.alert-time {
    color: #999;
}

.risk-suggestions {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-top: 10px;
}

.risk-suggestions h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.risk-suggestions ul {
    margin: 0;
    padding-left: 20px;
}

.risk-suggestions li {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.4;
}

.alert-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .works-grid {
        grid-template-columns: 1fr;
    }

    .archives-stats {
        grid-template-columns: 1fr;
    }

    .browser-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .search-input {
        width: 100%;
    }

    .risk-cards {
        grid-template-columns: 1fr;
    }

    .file-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .file-actions {
        justify-content: center;
    }

    .alert-item {
        flex-direction: column;
        gap: 15px;
    }

    .alert-actions {
        flex-direction: row;
        justify-content: center;
    }
}
