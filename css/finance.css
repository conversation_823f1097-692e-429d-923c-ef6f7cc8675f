/* 财务管理页面样式 */

/* 概览统计 */
.overview-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.card-trend.positive {
    color: #27ae60;
}

.card-trend.negative {
    color: #e74c3c;
}

.card-trend.warning {
    color: #f39c12;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3,
.chart-header h4 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-content {
    padding: 20px;
    height: 300px;
}

.recent-activities {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.activities-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activities-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.activities-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-item.urgent {
    border-left: 4px solid #e74c3c;
}

.activity-item.normal {
    border-left: 4px solid #27ae60;
}

.activity-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.activity-item.urgent .activity-icon {
    background: #ffebee;
    color: #e74c3c;
}

.activity-item.normal .activity-icon {
    background: #e8f5e8;
    color: #27ae60;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.activity-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.activity-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    background: #f0f0f0;
    color: #666;
}

.tag.urgent {
    background: #ffebee;
    color: #c62828;
}

.tag.info {
    background: #e3f2fd;
    color: #1976d2;
}

.tag.warning {
    background: #fff3e0;
    color: #ef6c00;
}

.tag.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.activity-time {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
}

/* 预算管理 */
.budget-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.budget-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.budget-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.budget-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.budget-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.budget-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.budget-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.budget-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.budget-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.budget-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.budget-management-tools {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.tools-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tools-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tools-actions {
    display: flex;
    gap: 12px;
}

.budget-filters {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.filter-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    min-width: 120px;
}

.filter-date {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.budget-table {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.progress-bar {
    position: relative;
    width: 100px;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 10px;
    position: relative;
    transition: width 0.3s;
}

.progress-fill.success {
    background: #27ae60;
}

.progress-fill.warning {
    background: #f39c12;
}

.progress-fill.danger {
    background: #e74c3c;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: white;
    z-index: 1;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.badge-success {
    background: #e8f5e8;
    color: #2e7d32;
}

.badge-warning {
    background: #fff3e0;
    color: #ef6c00;
}

.badge-danger {
    background: #ffebee;
    color: #c62828;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.budget-analysis {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.analysis-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analysis-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.analysis-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.time-range {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.analysis-content {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.budget-insights h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.insight-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.insight-item {
    display: flex;
    gap: 12px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.insight-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.insight-item.warning {
    background: #fff3e0;
    border-left: 4px solid #f39c12;
}

.insight-item.success {
    background: #e8f5e8;
    border-left: 4px solid #27ae60;
}

.insight-item.info {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.insight-item.warning .insight-icon {
    background: #f39c12;
    color: white;
}

.insight-item.success .insight-icon {
    background: #27ae60;
    color: white;
}

.insight-item.info .insight-icon {
    background: #2196f3;
    color: white;
}

.insight-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.insight-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

/* 费用管理与支付 */
.expense-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.expense-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.expense-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.expense-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.expense-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.expense-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.expense-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.expense-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.expense-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.expense-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.expense-workflow {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.workflow-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.workflow-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.workflow-steps {
    padding: 30px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
    position: relative;
}

.step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 30px;
    right: -10px;
    width: 20px;
    height: 2px;
    background: #ddd;
    z-index: 1;
}

.step-item.active {
    background: #f8f9ff;
    border: 2px solid #667eea;
}

.step-item.active::after {
    background: #667eea;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ddd;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 12px;
    position: relative;
    z-index: 2;
}

.step-item.active .step-number {
    background: #667eea;
}

.step-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.step-content p {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.step-count {
    display: inline-block;
    padding: 2px 8px;
    background: #667eea;
    color: white;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.payment-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.payment-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.payment-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.payment-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.payment-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.payment-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.payment-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.payment-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.payment-item.pending {
    border-left: 4px solid #f39c12;
}

.payment-item.completed {
    border-left: 4px solid #27ae60;
}

.payment-item.processing {
    border-left: 4px solid #3498db;
}

.payment-info {
    flex: 1;
}

.payment-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.payment-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.payment-amount {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 120px;
}

.amount {
    font-size: 18px;
    font-weight: 700;
    color: #333;
}

.status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
}

.status.pending {
    background: #fff3e0;
    color: #ef6c00;
}

.status.completed {
    background: #e8f5e8;
    color: #2e7d32;
}

.status.processing {
    background: #e3f2fd;
    color: #1976d2;
}

.payment-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .overview-stats,
    .budget-stats,
    .expense-stats {
        grid-template-columns: 1fr;
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .workflow-steps {
        grid-template-columns: 1fr;
    }

    .step-item::after {
        display: none;
    }

    .analysis-content {
        grid-template-columns: 1fr;
    }

    .tools-actions {
        flex-direction: column;
    }

    .action-buttons {
        flex-direction: column;
    }

    .payment-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .payment-amount {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .payment-actions {
        flex-direction: row;
        justify-content: center;
    }
}
