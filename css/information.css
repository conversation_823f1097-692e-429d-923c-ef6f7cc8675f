/* 信息发布管理页面样式 */

/* 概览统计 */
.overview-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.card-trend.positive {
    color: #27ae60;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3,
.chart-header h4 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-content {
    padding: 20px;
    height: 300px;
}

.recent-activities {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.activities-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activities-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.activities-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-item.urgent {
    border-left: 4px solid #e74c3c;
}

.activity-item.normal {
    border-left: 4px solid #27ae60;
}

.activity-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.activity-item.urgent .activity-icon {
    background: #ffebee;
    color: #e74c3c;
}

.activity-item.normal .activity-icon {
    background: #e8f5e8;
    color: #27ae60;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.activity-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.activity-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    background: #f0f0f0;
    color: #666;
}

.tag.urgent {
    background: #ffebee;
    color: #c62828;
}

.tag.info {
    background: #e3f2fd;
    color: #1976d2;
}

.tag.warning {
    background: #fff3e0;
    color: #ef6c00;
}

.activity-time {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
}

/* 多媒体内容管理 */
.content-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.content-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.content-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.content-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.content-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.content-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.content-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.content-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.content-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.content-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.content-editor {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.editor-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.editor-actions {
    display: flex;
    gap: 12px;
}

.editor-workspace {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
}

.editor-toolbar {
    display: flex;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.toolbar-group {
    display: flex;
    gap: 8px;
}

.toolbar-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.toolbar-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.content-type-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.editor-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.content-title {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
}

.content-editor {
    min-height: 400px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.6;
    outline: none;
}

.content-editor:focus {
    border-color: #667eea;
}

.editor-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.sidebar-section {
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
}

.sidebar-section h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    margin-bottom: 0;
}

.content-library {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.library-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.library-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.library-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
}

.category-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.content-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.content-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.content-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.content-thumbnail {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #667eea;
    flex-shrink: 0;
}

.content-info {
    flex: 1;
}

.content-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.content-info p {
    font-size: 13px;
    color: #666;
    margin-bottom: 10px;
}

.content-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.content-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 精准投放系统 */
.delivery-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.delivery-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.delivery-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.delivery-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.delivery-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.delivery-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.delivery-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.delivery-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.delivery-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.delivery-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.targeting-system {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.targeting-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.targeting-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.targeting-rules {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rule-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
}

.rule-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.rule-header {
    padding: 15px 20px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rule-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.rule-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.rule-status.active {
    background: #e8f5e8;
    color: #27ae60;
}

.rule-content {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rule-conditions {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.condition-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.condition-group label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.condition-value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.rule-actions {
    display: flex;
    gap: 8px;
}

.audience-analysis {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.analysis-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analysis-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.analysis-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.time-range {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.analysis-content {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.audience-insights h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.insight-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.insight-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.insight-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.insight-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.insight-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

/* 智能推送提醒 */
.push-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.push-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.push-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.push-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.push-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.push-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.push-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.push-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.push-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.push-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.scheduled-posts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.scheduled-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.scheduled-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.scheduled-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.scheduled-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.scheduled-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.scheduled-item.pending {
    border-left: 4px solid #f39c12;
}

.scheduled-item.sent {
    border-left: 4px solid #27ae60;
}

.schedule-time {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    min-width: 80px;
}

.time-display {
    text-align: center;
}

.time-display .date {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.time-display .time {
    display: block;
    font-size: 14px;
    color: #666;
}

.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
}

.status.pending {
    background: #fff3cd;
    color: #856404;
}

.status.sent {
    background: #d4edda;
    color: #155724;
}

.schedule-content {
    flex: 1;
}

.schedule-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.schedule-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 10px;
}

.schedule-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.schedule-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 阅读确认管理 */
.reading-confirmation {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.confirmation-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.confirmation-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.confirmation-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.status-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.confirmation-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.confirmation-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.confirmation-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.confirmation-item.urgent {
    border-left: 4px solid #e74c3c;
}

.confirmation-item.normal {
    border-left: 4px solid #3498db;
}

.confirmation-info {
    flex: 1;
}

.confirmation-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.confirmation-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.confirmation-stats {
    display: flex;
    gap: 20px;
    align-items: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.stat-value.confirmed {
    color: #27ae60;
}

.stat-value.pending {
    color: #f39c12;
}

.confirmation-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 多渠道推送配置 */
.multi-channel-push {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.channel-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.channel-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.channel-config {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.channel-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.channel-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.channel-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.channel-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.channel-details h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.channel-details p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.channel-status {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    font-weight: 500;
}

.channel-status.active {
    color: #27ae60;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 互动反馈机制 */
.interaction-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.interaction-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.interaction-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.interaction-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.interaction-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.interaction-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.interaction-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.interaction-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.interaction-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.interaction-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.recent-interactions {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.interactions-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.interactions-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.interaction-filters {
    display: flex;
    gap: 12px;
    align-items: center;
}

.interaction-type {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.interactions-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.interaction-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.interaction-item:hover {
    background: #f8f9fa;
}

.interaction-item.comment {
    border-left: 4px solid #3498db;
}

.interaction-item.feedback {
    border-left: 4px solid #f39c12;
}

.interaction-item.like {
    border-left: 4px solid #e91e63;
}

.interaction-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.interaction-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.interaction-content {
    flex: 1;
}

.interaction-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 14px;
}

.user-name {
    font-weight: 600;
    color: #333;
}

.interaction-type {
    color: #666;
}

.post-title {
    color: #667eea;
    font-weight: 500;
}

.interaction-time {
    color: #999;
    font-size: 12px;
    margin-left: auto;
}

.interaction-text {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.5;
}

.interaction-actions {
    display: flex;
    gap: 8px;
}

/* 数据分析报告 */
.analytics-dashboard {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.analytics-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analytics-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.analytics-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.time-period {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.analytics-content {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.analytics-insights h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.insight-cards {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.insight-card {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.insight-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.insight-card .insight-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.insight-card .insight-content h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.insight-card .insight-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.insight-value {
    font-size: 14px;
    font-weight: 600;
    color: #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .overview-stats,
    .content-stats,
    .delivery-stats,
    .push-stats,
    .interaction-stats {
        grid-template-columns: 1fr;
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .editor-workspace {
        grid-template-columns: 1fr;
    }

    .analysis-content,
    .analytics-content {
        grid-template-columns: 1fr;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .scheduled-item,
    .confirmation-item {
        flex-direction: column;
        gap: 15px;
    }

    .confirmation-stats {
        justify-content: space-around;
    }
}
