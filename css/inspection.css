/* 巡检管理页面样式 */

/* 概览统计 */
.overview-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.card-trend.positive {
    color: #27ae60;
}

.card-trend.negative {
    color: #e74c3c;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-content {
    padding: 20px;
    height: 300px;
}

.recent-activities {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.activity-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.activity-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-item.completed {
    border-left: 4px solid #27ae60;
}

.activity-item.warning {
    border-left: 4px solid #f39c12;
}

.activity-item.in-progress {
    border-left: 4px solid #3498db;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.activity-item.completed .activity-icon {
    background: #e8f5e8;
    color: #27ae60;
}

.activity-item.warning .activity-icon {
    background: #fff3e0;
    color: #f39c12;
}

.activity-item.in-progress .activity-icon {
    background: #e3f2fd;
    color: #3498db;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.activity-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.activity-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    background: #f0f0f0;
    color: #666;
}

.tag.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.tag.warning {
    background: #fff3e0;
    color: #ef6c00;
}

.tag.info {
    background: #e3f2fd;
    color: #1976d2;
}

.activity-time {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
}

/* 智能路径规划 */
.planning-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.route-optimization {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.optimization-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.optimization-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.optimization-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.area-selector {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.route-content {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.route-map {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

.map-container {
    position: relative;
    height: 400px;
}

.map-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 18px;
}

.map-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #667eea;
}

.route-info {
    position: absolute;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #666;
}

.info-value {
    font-weight: 600;
    color: #333;
}

.info-value.success {
    color: #27ae60;
}

.route-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.route-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.route-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.route-actions {
    display: flex;
    gap: 8px;
}

.route-steps {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.step-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.step-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.step-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.step-meta {
    display: flex;
    gap: 10px;
    align-items: center;
}

.step-time {
    font-size: 12px;
    color: #999;
}

.step-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.step-priority.high {
    background: #ffebee;
    color: #c62828;
}

.step-priority.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.step-status {
    color: #999;
    font-size: 18px;
}

.navigation-support {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.navigation-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.navigation-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.navigation-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.navigation-status.online {
    color: #27ae60;
}

.navigation-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.current-task {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.task-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.task-info p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.task-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s;
}

.progress-text {
    font-size: 13px;
    font-weight: 600;
    color: #667eea;
}

.navigation-alerts {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #f39c12;
    background: #fff3e0;
}

.alert-item.warning {
    border-left-color: #f39c12;
    background: #fff3e0;
}

.alert-item i {
    color: #f39c12;
    font-size: 18px;
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
}

.alert-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.alert-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

/* 缺陷AI识别 */
.ai-detection-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.detection-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.detection-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detection-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.detection-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.detection-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.detection-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.detection-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.detection-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.detection-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.ai-detection-tools {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.tool-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.tool-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tool-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tool-content {
    padding: 20px;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s;
}

.upload-area:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.upload-placeholder i {
    font-size: 48px;
    color: #667eea;
    margin-bottom: 15px;
}

.upload-placeholder p {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
}

.upload-placeholder span {
    font-size: 14px;
    color: #666;
}

.detection-results {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.result-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.result-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-image {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.detection-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.detection-box {
    position: absolute;
    border: 2px solid #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 10px;
    color: #e74c3c;
    font-weight: 600;
}

.detection-label {
    background: #e74c3c;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    margin-bottom: 2px;
}

.confidence-score {
    background: rgba(255,255,255,0.9);
    padding: 2px 6px;
    border-radius: 4px;
}

.result-details {
    flex: 1;
}

.result-details h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.result-details p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.result-actions {
    display: flex;
    gap: 8px;
}

.voice-recorder {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.recorder-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 8px;
}

.waveform {
    display: flex;
    align-items: center;
    gap: 4px;
}

.wave-bar {
    width: 4px;
    height: 20px;
    background: #667eea;
    border-radius: 2px;
    animation: wave 1s ease-in-out infinite;
}

.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes wave {
    0%, 100% { height: 20px; }
    50% { height: 40px; }
}

.recording-time {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.transcription-result {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.transcription-result h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.transcription-result p {
    font-size: 14px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 15px;
    font-style: italic;
}

.ai-analysis {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.ai-analysis h6 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.ai-analysis ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ai-analysis li {
    font-size: 13px;
    color: #666;
    margin-bottom: 6px;
    padding-left: 15px;
    position: relative;
}

.ai-analysis li:before {
    content: "•";
    color: #667eea;
    position: absolute;
    left: 0;
}

/* 隐患闭环管理 */
.hazard-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.hazard-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.hazard-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.hazard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.hazard-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.hazard-stat:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hazard-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.hazard-stat .stat-icon.pending {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.hazard-stat .stat-icon.processing {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.hazard-stat .stat-icon.completed {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.hazard-stat .stat-icon.rate {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.hazard-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.hazard-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.hazard-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.hazard-workflow {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.workflow-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.workflow-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.workflow-content {
    padding: 30px;
}

.workflow-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
}

.workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.workflow-step.active .step-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.workflow-step .step-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #f0f0f0;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: 15px;
    transition: all 0.3s;
}

.workflow-step .step-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.workflow-step .step-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.workflow-arrow {
    color: #ddd;
    font-size: 20px;
    margin: 0 10px;
}

.hazard-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.status-filter,
.priority-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.hazard-items {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.hazard-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.hazard-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hazard-item.urgent {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.hazard-item.high {
    border-left: 4px solid #f39c12;
}

.hazard-item.processing {
    border-left: 4px solid #3498db;
    background: linear-gradient(135deg, #e3f2fd 0%, rgba(227, 242, 253, 0.3) 100%);
}

.hazard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.hazard-id {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #333;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.hazard-priority,
.hazard-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.hazard-priority.urgent {
    background: #ffebee;
    color: #c62828;
}

.hazard-priority.high {
    background: #fff3e0;
    color: #ef6c00;
}

.hazard-status.pending {
    background: #fff3e0;
    color: #ef6c00;
}

.hazard-status.processing {
    background: #e3f2fd;
    color: #1976d2;
}

.hazard-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.hazard-content > p {
    font-size: 13px;
    color: #666;
    margin-bottom: 12px;
}

.hazard-description p {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 12px;
}

.hazard-images {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.hazard-images img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
}

.hazard-timeline {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.timeline-item.completed {
    color: #27ae60;
}

.timeline-item.pending {
    color: #999;
}

.timeline-time {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 4px;
}

.timeline-content {
    font-size: 11px;
}

.processing-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.processing-info p {
    font-size: 13px;
    color: #333;
    margin-bottom: 8px;
}

.processing-info p:last-of-type {
    margin-bottom: 12px;
}

.hazard-actions {
    display: flex;
    gap: 8px;
}

/* 设备健康评级 */
.health-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.health-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.health-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.health-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: center;
}

.health-chart {
    height: 250px;
}

.health-stats {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.health-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.health-stat:hover {
    transform: translateX(5px);
}

.health-stat.excellent {
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.5) 100%);
    border-left: 4px solid #27ae60;
}

.health-stat.good {
    background: linear-gradient(135deg, #e3f2fd 0%, rgba(227, 242, 253, 0.5) 100%);
    border-left: 4px solid #3498db;
}

.health-stat.warning {
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.5) 100%);
    border-left: 4px solid #f39c12;
}

.health-stat.critical {
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.5) 100%);
    border-left: 4px solid #e74c3c;
}

.health-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.health-stat .stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.equipment-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.equipment-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.equipment-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.equipment-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.category-filter,
.health-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.equipment-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.equipment-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.equipment-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.equipment-card.excellent {
    border-left: 4px solid #27ae60;
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.3) 100%);
}

.equipment-card.warning {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.equipment-card .equipment-header {
    padding: 0;
    border: none;
    margin-bottom: 15px;
}

.equipment-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.equipment-info {
    flex: 1;
}

.equipment-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.equipment-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.health-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 80px;
}

.score-number {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 4px;
}

.score-label {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
}

.health-score.excellent .score-number {
    color: #27ae60;
}

.health-score.excellent .score-label {
    background: #e8f5e8;
    color: #2e7d32;
}

.health-score.warning .score-number {
    color: #f39c12;
}

.health-score.warning .score-label {
    background: #fff3e0;
    color: #ef6c00;
}

.equipment-metrics {
    margin-bottom: 15px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.metric-item:last-child {
    margin-bottom: 0;
}

.metric-label {
    color: #666;
}

.metric-value {
    font-weight: 600;
}

.metric-value.normal {
    color: #27ae60;
}

.metric-value.warning {
    color: #f39c12;
}

.metric-value.low {
    color: #27ae60;
}

.metric-value.medium {
    color: #f39c12;
}

.metric-value.high {
    color: #e74c3c;
}

.metric-value.good {
    color: #27ae60;
}

.health-trend {
    height: 80px;
    margin-bottom: 15px;
}

.maintenance-alert {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: #fff3e0;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #ef6c00;
}

.maintenance-alert i {
    color: #f39c12;
}

.equipment-actions {
    display: flex;
    gap: 8px;
}

/* 巡检效能分析 */
.efficiency-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.efficiency-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.efficiency-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.efficiency-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.efficiency-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.efficiency-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.efficiency-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.efficiency-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.efficiency-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.efficiency-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.analysis-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.optimization-suggestions {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.suggestions-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestions-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.suggestions-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.suggestion-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.suggestion-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.suggestion-item.high-priority {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.suggestion-item.medium-priority {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.suggestion-item.low-priority {
    border-left: 4px solid #3498db;
    background: linear-gradient(135deg, #e3f2fd 0%, rgba(227, 242, 253, 0.3) 100%);
}

.suggestion-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.suggestion-item.high-priority .suggestion-icon {
    background: #ffebee;
    color: #e74c3c;
}

.suggestion-item.medium-priority .suggestion-icon {
    background: #fff3e0;
    color: #f39c12;
}

.suggestion-item.low-priority .suggestion-icon {
    background: #e3f2fd;
    color: #3498db;
}

.suggestion-content {
    flex: 1;
}

.suggestion-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.suggestion-content p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 10px;
}

.suggestion-impact {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.impact-label {
    color: #666;
}

.impact-value {
    font-weight: 600;
    color: #27ae60;
}

.suggestion-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .route-content {
        grid-template-columns: 1fr;
    }

    .health-summary {
        grid-template-columns: 1fr;
    }

    .analysis-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .overview-stats,
    .detection-stats,
    .hazard-stats,
    .efficiency-stats {
        grid-template-columns: 1fr;
    }

    .optimization-header,
    .navigation-header,
    .workflow-header,
    .list-header,
    .equipment-header,
    .suggestions-header {
        flex-direction: column;
        gap: 15px;
    }

    .optimization-controls,
    .list-controls,
    .equipment-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .workflow-steps {
        flex-direction: column;
        gap: 30px;
    }

    .workflow-arrow {
        transform: rotate(90deg);
        margin: 10px 0;
    }

    .equipment-grid {
        grid-template-columns: 1fr;
    }

    .equipment-card .equipment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .hazard-item,
    .suggestion-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .hazard-actions,
    .equipment-actions,
    .suggestion-actions {
        flex-direction: row;
        justify-content: center;
    }

    .hazard-timeline {
        flex-direction: column;
        gap: 10px;
    }

    .timeline-item {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }
}
