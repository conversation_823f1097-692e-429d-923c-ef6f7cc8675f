/* 报修管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 维修概览 */
.maintenance-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    flex-shrink: 0;
}

.card-icon.urgent {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.card-trend.up {
    color: #27ae60;
}

.card-trend.down {
    color: #e74c3c;
}

.card-trend.stable {
    color: #95a5a6;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 智能故障诊断 */
.diagnosis-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.diagnosis-input,
.diagnosis-result {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.input-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-header p {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    height: 80px;
    resize: vertical;
}

.diagnose-btn {
    width: 100%;
    padding: 12px;
    margin-top: 10px;
}

/* 诊断结果 */
.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.result-header h4 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.confidence-score {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.score-bar {
    width: 80px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border-radius: 3px;
    transition: width 0.3s;
}

.fault-analysis,
.solution-suggestions {
    margin-bottom: 25px;
}

.fault-analysis h5,
.solution-suggestions h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.fault-list {
    list-style: none;
    padding: 0;
}

.fault-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.fault-list li:last-child {
    border-bottom: none;
}

.probability {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    min-width: 40px;
    text-align: center;
}

.fault-desc {
    flex: 1;
    font-size: 14px;
    color: #333;
}

/* 解决方案步骤 */
.solution-steps {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.step {
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content h6 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.step-content p {
    font-size: 13px;
    color: #666;
    line-height: 1.5;
}

.result-actions {
    display: flex;
    gap: 10px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

/* 维修调度 */
.scheduling-dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.technician-status,
.work-orders {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.technician-status h3,
.work-orders h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.technician-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.technician-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s;
}

.technician-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.technician-card.available {
    border-left: 4px solid #27ae60;
}

.technician-card.busy {
    border-left: 4px solid #f39c12;
}

.technician-card .technician-avatar {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
}

.technician-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.technician-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.technician-info p {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.skill-tags {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.skill-tag {
    background: #f8f9fa;
    color: #667eea;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.technician-status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-left: auto;
}

.technician-status-badge.available {
    background: #d4edda;
    color: #155724;
}

.technician-status-badge.busy {
    background: #fff3cd;
    color: #856404;
}

.workload {
    margin-top: 10px;
}

.workload span {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
    display: block;
}

.workload-bar {
    width: 100%;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
}

.workload-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    transition: width 0.3s;
}

/* 工单列表 */
.orders-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.orders-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.order-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s;
}

.order-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.order-item.urgent {
    border-left: 4px solid #e74c3c;
}

.order-item.normal {
    border-left: 4px solid #f39c12;
}

.order-priority {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
    font-size: 12px;
    font-weight: 600;
}

.order-priority.urgent {
    color: #e74c3c;
}

.order-priority.normal {
    color: #f39c12;
}

.order-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.order-info p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.order-meta {
    display: flex;
    gap: 15px;
    font-size: 11px;
    color: #999;
    margin-bottom: 10px;
}

.order-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.assign-select {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .diagnosis-panel,
    .scheduling-dashboard {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .order-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

/* 服务商评价 */
.evaluation-overview {
    margin-bottom: 30px;
}

.vendor-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.vendor-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.vendor-cards {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.vendor-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.vendor-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.vendor-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.vendor-logo {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.vendor-info {
    flex: 1;
}

.vendor-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.vendor-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.vendor-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rating-stars {
    color: #ffc107;
}

.rating-score {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.vendor-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 15px;
    padding: 15px 0;
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
}

.metric {
    text-align: center;
}

.metric-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.metric-value {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.vendor-actions {
    display: flex;
    gap: 8px;
}

/* 预防性维护 */
.prevention-dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.maintenance-schedule,
.maintenance-alerts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.maintenance-schedule h3,
.maintenance-alerts h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.schedule-calendar {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-header {
    background: #f8f9fa;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.calendar-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.calendar-nav {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.calendar-nav:hover {
    background: rgba(102, 126, 234, 0.1);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #f0f0f0;
}

.calendar-day {
    background: white;
    padding: 10px;
    min-height: 80px;
    position: relative;
}

.day-number {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    display: block;
}

.day-events {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.event {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    text-align: center;
}

.event.urgent {
    background: #ffebee;
    color: #c62828;
}

.event.normal {
    background: #e3f2fd;
    color: #1565c0;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.alert-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.alert-item.overdue {
    border-left: 4px solid #e74c3c;
}

.alert-item.upcoming {
    border-left: 4px solid #f39c12;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.alert-item.overdue .alert-icon {
    background: #e74c3c;
}

.alert-item.upcoming .alert-icon {
    background: #f39c12;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.alert-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.alert-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
}

.alert-actions {
    display: flex;
    gap: 8px;
}

/* 维修知识库 */
.knowledge-base {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.knowledge-search {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.search-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-bar {
    display: flex;
    margin-bottom: 15px;
}

.knowledge-search-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-right: none;
    border-radius: 6px 0 0 6px;
    font-size: 14px;
    outline: none;
}

.knowledge-search-input:focus {
    border-color: #667eea;
}

.search-btn {
    padding: 12px 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background: #5a6fd8;
}

.search-filters {
    display: flex;
    gap: 15px;
}

.knowledge-categories {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.knowledge-categories h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.category-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
    cursor: pointer;
}

.category-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin: 0 auto 15px;
}

.category-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.category-card p {
    font-size: 14px;
    color: #666;
}

.knowledge-articles {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.articles-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.articles-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.articles-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.article-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.article-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.article-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.article-content {
    flex: 1;
}

.article-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
}

.article-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.5;
}

.article-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
}

.article-meta .category {
    background: #f8f9fa;
    color: #667eea;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.article-actions {
    display: flex;
    gap: 8px;
}

/* 按钮样式 */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .vendor-stats {
        grid-template-columns: 1fr;
    }

    .vendor-cards {
        grid-template-columns: 1fr;
    }

    .prevention-dashboard {
        grid-template-columns: 1fr;
    }

    .calendar-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .category-grid {
        grid-template-columns: 1fr;
    }

    .search-filters {
        flex-direction: column;
    }

    .article-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .article-actions {
        justify-content: center;
    }
}
