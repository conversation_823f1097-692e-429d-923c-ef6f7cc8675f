/* 医废管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 医废概览 */
.waste-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    flex-shrink: 0;
}

.card-icon.success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.card-icon.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.card-icon.info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-number .unit {
    font-size: 16px;
    color: #999;
    font-weight: 400;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.card-trend.up {
    color: #27ae60;
}

.card-trend.down {
    color: #e74c3c;
}

.card-trend.stable {
    color: #95a5a6;
}

.card-detail {
    font-size: 13px;
    color: #666;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.tab-btn.active {
    background: #e74c3c;
    color: white;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 医废智能分类 */
.classification-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.classification-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.classification-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.classification-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
}

.stat-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stat-card.infectious {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border-color: #f8bbd9;
}

.stat-card.pathological {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-color: #ffcc80;
}

.stat-card.chemical {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-color: #a5d6a7;
}

.stat-card.pharmaceutical {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: #90caf9;
}

.stat-card .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.stat-card.infectious .stat-icon {
    background: #e74c3c;
}

.stat-card.pathological .stat-icon {
    background: #f39c12;
}

.stat-card.chemical .stat-icon {
    background: #27ae60;
}

.stat-card.pharmaceutical .stat-icon {
    background: #3498db;
}

.stat-card .stat-content h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
}

.stat-card .stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.stat-card .stat-percentage {
    font-size: 12px;
    color: #666;
}

/* 扫码分类 */
.classification-scanner {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.scanner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.scanner-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.scanner-interface {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.scanner-area {
    text-align: center;
}

.scanner-frame {
    width: 200px;
    height: 200px;
    border: 2px dashed #e74c3c;
    border-radius: 8px;
    margin: 0 auto 15px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
}

.scan-line {
    width: 100%;
    height: 2px;
    background: #e74c3c;
    position: absolute;
    animation: scan 2s linear infinite;
}

@keyframes scan {
    0% { top: 0; }
    100% { top: 100%; }
}

.corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #e74c3c;
}

.corner.top-left {
    top: -2px;
    left: -2px;
    border-right: none;
    border-bottom: none;
}

.corner.top-right {
    top: -2px;
    right: -2px;
    border-left: none;
    border-bottom: none;
}

.corner.bottom-left {
    bottom: -2px;
    left: -2px;
    border-right: none;
    border-top: none;
}

.corner.bottom-right {
    bottom: -2px;
    right: -2px;
    border-left: none;
    border-top: none;
}

.scanner-tip {
    color: #666;
    font-size: 14px;
}

.classification-result h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.result-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    align-items: center;
}

.result-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.result-icon.infectious {
    background: #e74c3c;
}

.result-info {
    flex: 1;
}

.result-info h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.result-info p {
    font-size: 13px;
    color: #666;
    margin: 2px 0;
}

.result-actions {
    display: flex;
    gap: 8px;
}

/* 医废分类管理 */
.waste-categories {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.categories-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.categories-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.category-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.category-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.categories-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.category-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.category-card:hover {
    border-color: #e74c3c;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.1);
}

.category-card.infectious {
    border-left: 4px solid #e74c3c;
}

.category-card.pathological {
    border-left: 4px solid #f39c12;
}

.category-card.chemical {
    border-left: 4px solid #27ae60;
}

.category-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.category-card.infectious .category-icon {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.category-card.pathological .category-icon {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.category-card.chemical .category-icon {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.category-info {
    flex: 1;
}

.category-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.category-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.category-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.category-status.active {
    background: #d4edda;
    color: #155724;
}

.category-details {
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 13px;
    color: #666;
}

.detail-value {
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

.detail-value.high {
    color: #e74c3c;
}

.detail-value.medium {
    color: #f39c12;
}

.category-actions {
    display: flex;
    gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .classification-stats {
        grid-template-columns: 1fr;
    }
    
    .scanner-interface {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .categories-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .category-controls {
        flex-direction: column;
        align-items: stretch;
    }
}

/* 转运轨迹追踪 */
.tracking-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.tracking-map {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.tracking-map h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.map-container {
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.map-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 18px;
}

.map-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #e74c3c;
}

.map-legend {
    position: absolute;
    top: 15px;
    right: 15px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 12px;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-color.in-transit {
    background: #3498db;
}

.legend-color.completed {
    background: #27ae60;
}

.legend-color.delayed {
    background: #e74c3c;
}

.tracking-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tracking-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.status-filter,
.date-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.tracking-items {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.tracking-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s;
}

.tracking-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tracking-item.in-transit {
    border-left: 4px solid #3498db;
}

.tracking-item.completed {
    border-left: 4px solid #27ae60;
}

.tracking-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 5px;
}

.indicator-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-bottom: 8px;
}

.tracking-item.in-transit .indicator-dot {
    background: #3498db;
    animation: pulse 2s infinite;
}

.tracking-item.completed .indicator-dot {
    background: #27ae60;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
    100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

.indicator-line {
    width: 2px;
    height: 50px;
    background: #f0f0f0;
}

.tracking-content {
    flex: 1;
}

.tracking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.tracking-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.tracking-status {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.tracking-status.in-transit {
    background: #e3f2fd;
    color: #1976d2;
}

.tracking-status.completed {
    background: #e8f5e8;
    color: #2e7d32;
}

.tracking-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
}

.detail-row .detail-label {
    color: #666;
}

.detail-row .detail-value {
    font-weight: 600;
    color: #333;
}

.tracking-timeline {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.timeline-item {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    position: relative;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-top: 4px;
    flex-shrink: 0;
}

.timeline-item.completed .timeline-dot {
    background: #27ae60;
}

.timeline-item.current .timeline-dot {
    background: #3498db;
    animation: pulse 2s infinite;
}

.timeline-item.pending .timeline-dot {
    background: #bdc3c7;
}

.timeline-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.timeline-content p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.tracking-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 合规监管 */
.compliance-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.compliance-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.compliance-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.compliance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.compliance-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.compliance-stat:hover {
    border-color: #e74c3c;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.1);
}

.compliance-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.compliance-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.compliance-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.compliance-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.compliance-records {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.records-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.records-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.records-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.compliance-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.records-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.record-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.record-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.record-item.compliant {
    border-left: 4px solid #27ae60;
}

.record-item.non-compliant {
    border-left: 4px solid #e74c3c;
}

.record-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 5px;
}

.record-item.compliant .record-indicator {
    background: #e8f5e8;
    color: #27ae60;
}

.record-item.non-compliant .record-indicator {
    background: #ffebee;
    color: #e74c3c;
}

.record-content {
    flex: 1;
}

.record-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.record-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.record-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.record-details .detail-value.compliant {
    color: #27ae60;
    font-weight: 600;
}

.record-details .detail-value.non-compliant {
    color: #e74c3c;
    font-weight: 600;
}

.record-meta {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #999;
}

.record-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .tracking-controls,
    .records-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .tracking-item,
    .record-item {
        flex-direction: column;
        gap: 15px;
    }

    .tracking-actions,
    .record-actions {
        flex-direction: row;
        justify-content: center;
    }

    .tracking-details,
    .record-details {
        grid-template-columns: 1fr;
    }

    .record-meta {
        flex-direction: column;
        gap: 8px;
    }
}

/* 预警上报系统 */
.alerts-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.alerts-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.alerts-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alerts-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.alert-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
}

.alert-stat:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-stat.high {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border-color: #f8bbd9;
}

.alert-stat.medium {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-color: #ffcc80;
}

.alert-stat.resolved {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-color: #a5d6a7;
}

.alert-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.alert-stat.high .stat-icon {
    background: #e74c3c;
}

.alert-stat.medium .stat-icon {
    background: #f39c12;
}

.alert-stat.resolved .stat-icon {
    background: #27ae60;
}

.alert-stat .stat-content h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
}

.alert-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.alert-stat .stat-content p {
    font-size: 12px;
    color: #666;
}

.alerts-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.alerts-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alerts-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alerts-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.alert-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.alerts-container {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.alert-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.alert-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-item.high-priority {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.alert-item.medium-priority {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.alert-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 5px;
}

.alert-item.high-priority .alert-indicator {
    background: #e74c3c;
    color: white;
    animation: pulse 2s infinite;
}

.alert-item.medium-priority .alert-indicator {
    background: #f39c12;
    color: white;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.alert-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.alert-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.alert-actions-inline {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 环境监测联动 */
.environment-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.environment-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.environment-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.environment-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.env-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.env-stat:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.env-stat.air {
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.3) 100%);
}

.env-stat.water {
    background: linear-gradient(135deg, #e3f2fd 0%, rgba(227, 242, 253, 0.3) 100%);
}

.env-stat.emission {
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.env-stat.noise {
    background: linear-gradient(135deg, #f3e5f5 0%, rgba(243, 229, 245, 0.3) 100%);
}

.env-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.env-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.env-stat .stat-number {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.env-stat .stat-value {
    font-size: 12px;
    color: #999;
}

.environment-charts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.chart-section h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.chart-container {
    height: 250px;
    position: relative;
}

.environment-alerts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.env-alerts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.env-alerts-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.env-alerts-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.env-alert-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.env-alert-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.env-alert-item.normal {
    border-left: 4px solid #27ae60;
}

.env-alert-item.warning {
    border-left: 4px solid #f39c12;
}

.env-alert-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    flex-shrink: 0;
}

.env-alert-item.normal .env-alert-icon {
    background: #e8f5e8;
    color: #27ae60;
}

.env-alert-item.warning .env-alert-icon {
    background: #fff3e0;
    color: #f39c12;
}

.env-alert-content {
    flex: 1;
}

.env-alert-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
}

.env-alert-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
}

.alert-time {
    font-size: 11px;
    color: #999;
}

/* 额外的预警和环境监测响应式样式 */
@media (max-width: 768px) {
    .alerts-stats,
    .environment-stats {
        grid-template-columns: 1fr;
    }

    .alerts-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .alert-item {
        flex-direction: column;
        gap: 15px;
    }

    .alert-actions-inline {
        justify-content: center;
    }

    .environment-charts {
        grid-template-columns: 1fr;
    }

    .env-alerts-header {
        flex-direction: column;
        gap: 15px;
    }
}
