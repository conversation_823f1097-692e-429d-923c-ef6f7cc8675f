/* 采购管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 数据概览 */
.overview-section {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 智能采购模块 */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.content-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: between;
    align-items: center;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-body {
    padding: 20px;
}

/* 分析项目 */
.analysis-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f5f5f5;
}

.analysis-item:last-child {
    border-bottom: none;
}

.analysis-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.analysis-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
}

.analysis-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.5;
}

.analysis-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
}

.confidence {
    color: #27ae60;
    font-weight: 500;
}

.trend {
    font-weight: 500;
}

.trend.up {
    color: #27ae60;
}

.trend.urgent {
    color: #e74c3c;
}

/* 计划时间线 */
.plan-timeline {
    position: relative;
}

.timeline-item {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 30px;
    top: 50px;
    width: 2px;
    height: calc(100% - 20px);
    background: #e9ecef;
}

.timeline-date {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
    text-align: center;
}

.timeline-content {
    flex: 1;
    padding-top: 8px;
}

.timeline-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.timeline-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status.pending {
    background: #fff3cd;
    color: #856404;
}

.status.planned {
    background: #d4edda;
    color: #155724;
}

.status.draft {
    background: #f8d7da;
    color: #721c24;
}

/* 图表区域 */
.chart-section {
    margin-bottom: 30px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.chart-select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* 供应商管理 */
.suppliers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    gap: 20px;
}

.search-bar {
    display: flex;
    flex: 1;
    max-width: 400px;
}

.search-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-right: none;
    border-radius: 6px 0 0 6px;
    font-size: 14px;
    outline: none;
}

.search-input:focus {
    border-color: #667eea;
}

.search-btn {
    padding: 10px 15px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background: #5a6fd8;
}

.filter-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

/* 供应商网格 */
.suppliers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.supplier-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 20px;
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
}

.supplier-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.supplier-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.supplier-avatar {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.supplier-info {
    flex: 1;
}

.supplier-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.supplier-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.supplier-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.supplier-status.active {
    background: #d4edda;
    color: #155724;
}

.supplier-status.evaluation {
    background: #fff3cd;
    color: #856404;
}

.supplier-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 15px;
    padding: 15px 0;
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.supplier-actions {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

/* 招标平台 */
.bidding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.bidding-stats {
    display: flex;
    gap: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.stat-content h4 {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.bidding-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.bidding-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.bidding-item:last-child {
    border-bottom: none;
}

.bidding-info {
    flex: 1;
}

.bidding-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
}

.bidding-info p {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.bidding-meta {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #999;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.bidding-status {
    text-align: center;
    min-width: 120px;
}

.progress-bar {
    width: 100px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    margin: 8px auto;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    transition: width 0.3s;
}

.progress-text {
    font-size: 11px;
    color: #666;
}

.bidding-actions {
    display: flex;
    gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .suppliers-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .suppliers-grid {
        grid-template-columns: 1fr;
    }
    
    .bidding-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .bidding-stats {
        flex-direction: column;
    }
    
    .bidding-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
}

/* 合同系统 */
.contracts-header {
    margin-bottom: 30px;
}

.template-section {
    margin-bottom: 30px;
}

.template-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.template-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
}

.template-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    border-color: #667eea;
}

.template-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin: 0 auto 15px;
}

.template-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.template-card p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.contracts-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.contracts-table {
    overflow-x: auto;
}

.contracts-table table {
    width: 100%;
    border-collapse: collapse;
}

.contracts-table th,
.contracts-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.contracts-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.contracts-table td {
    font-size: 14px;
    color: #666;
}

.contracts-table tr:hover {
    background: #f8f9fa;
}

.status.signed {
    background: #d4edda;
    color: #155724;
}

.status.approval {
    background: #fff3cd;
    color: #856404;
}

/* 效能评估 */
.evaluation-overview {
    margin-bottom: 30px;
}

.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.kpi-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.kpi-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.kpi-content {
    flex: 1;
}

.kpi-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.kpi-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.kpi-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.kpi-trend.up {
    color: #27ae60;
}

.kpi-trend.down {
    color: #e74c3c;
}

.kpi-trend.stable {
    color: #95a5a6;
}

.evaluation-charts {
    margin-bottom: 30px;
}

.chart-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.evaluation-reports {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.reports-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.reports-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.reports-list {
    padding: 20px;
}

.report-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.report-item:last-child {
    border-bottom: none;
}

.report-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.report-info {
    flex: 1;
}

.report-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.report-info p {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.report-date {
    font-size: 12px;
    color: #999;
}

.report-actions {
    display: flex;
    gap: 8px;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .template-grid {
        grid-template-columns: 1fr;
    }

    .list-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .list-controls {
        justify-content: space-between;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
    }

    .chart-row {
        grid-template-columns: 1fr;
    }

    .report-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .report-actions {
        justify-content: center;
    }
}
