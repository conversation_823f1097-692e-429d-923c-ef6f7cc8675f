/* 物业管理系统页面样式 */

/* 概览统计 */
.overview-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.card-trend.positive {
    color: #27ae60;
}

.card-trend.negative {
    color: #e74c3c;
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.chart-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-filter {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-content {
    padding: 20px;
    height: 300px;
}

.recent-activities {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.activities-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activities-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.activities-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.activity-item:hover {
    background: #f8f9fa;
}

.activity-item.urgent {
    border-left: 4px solid #e74c3c;
}

.activity-item.normal {
    border-left: 4px solid #27ae60;
}

.activity-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.activity-item.urgent .activity-icon {
    background: #ffebee;
    color: #e74c3c;
}

.activity-item.normal .activity-icon {
    background: #e8f5e8;
    color: #27ae60;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.activity-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.activity-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    background: #f0f0f0;
    color: #666;
}

.tag.urgent {
    background: #ffebee;
    color: #c62828;
}

.tag.info {
    background: #e3f2fd;
    color: #1976d2;
}

.tag.warning {
    background: #fff3e0;
    color: #ef6c00;
}

.tag.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.activity-time {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
}

/* 业主管理 */
.owner-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.owner-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.owner-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.owner-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.owner-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.owner-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.owner-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.owner-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.owner-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.owner-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.owner-management-tools {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.tools-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tools-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tools-actions {
    display: flex;
    gap: 12px;
}

.search-filters {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    min-width: 120px;
}

.owner-table {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.owner-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.owner-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.badge-info {
    background: #e3f2fd;
    color: #1976d2;
}

.badge-warning {
    background: #fff3e0;
    color: #ef6c00;
}

.badge-success {
    background: #e8f5e8;
    color: #2e7d32;
}

.badge-danger {
    background: #ffebee;
    color: #c62828;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.pagination {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.page-info {
    font-size: 14px;
    color: #666;
}

/* 设施预订 */
.facility-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.facility-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.facility-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.facility-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.facility-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.facility-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.facility-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.facility-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.facility-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.facility-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.booking-calendar {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.calendar-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calendar-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.calendar-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.facility-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.calendar-view {
    padding: 20px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 20px;
}

.time-slots {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.time-slot {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.facility-columns {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.facility-column {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.facility-header {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 6px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.booking-slot {
    height: 60px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.booking-slot.available {
    background: #e8f5e8;
    border: 2px dashed #27ae60;
}

.booking-slot.available:hover {
    background: #d4edda;
}

.booking-slot.booked {
    background: #667eea;
    color: white;
}

.booking-info {
    text-align: center;
    padding: 8px;
}

.booking-info h5 {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 4px;
}

.booking-info p {
    font-size: 10px;
    opacity: 0.9;
    margin: 0;
}

.recent-bookings {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.bookings-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bookings-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bookings-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.booking-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s;
}

.booking-item:hover {
    background: #f8f9fa;
}

.booking-item.confirmed {
    border-left: 4px solid #27ae60;
}

.booking-item.pending {
    border-left: 4px solid #f39c12;
}

.booking-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.booking-item.confirmed .booking-icon {
    background: #e8f5e8;
    color: #27ae60;
}

.booking-item.pending .booking-icon {
    background: #fff3e0;
    color: #f39c12;
}

.booking-content {
    flex: 1;
}

.booking-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.booking-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.booking-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.booking-time {
    font-size: 12px;
    color: #999;
    white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .overview-stats,
    .owner-stats,
    .facility-stats {
        grid-template-columns: 1fr;
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .calendar-grid {
        grid-template-columns: 1fr;
    }

    .facility-columns {
        grid-template-columns: 1fr;
    }

    .tools-actions {
        flex-direction: column;
    }

    .action-buttons {
        flex-direction: column;
    }
}
