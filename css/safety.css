/* 安全生产管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 安全概览 */
.safety-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    flex-shrink: 0;
}

.card-icon.success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.card-icon.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.card-icon.info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.card-trend.up {
    color: #27ae60;
}

.card-trend.down {
    color: #e74c3c;
}

.card-trend.stable {
    color: #95a5a6;
}

.card-detail {
    font-size: 13px;
    color: #666;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 风险分级管控 */
.risk-control-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.risk-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.risk-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.risk-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.risk-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
}

.risk-stat.high {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border: 1px solid #f8bbd9;
}

.risk-stat.medium {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border: 1px solid #ffcc80;
}

.risk-stat.low {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border: 1px solid #a5d6a7;
}

.risk-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.risk-stat.high .stat-icon {
    background: #e74c3c;
}

.risk-stat.medium .stat-icon {
    background: #f39c12;
}

.risk-stat.low .stat-icon {
    background: #27ae60;
}

.risk-stat .stat-content h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
}

.risk-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.risk-stat .stat-content p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.risk-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.risk-filter,
.area-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.risk-items {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.risk-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s;
}

.risk-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.risk-item.high-risk {
    border-left: 4px solid #e74c3c;
}

.risk-item.medium-risk {
    border-left: 4px solid #f39c12;
}

.risk-item.low-risk {
    border-left: 4px solid #27ae60;
}

.risk-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 4px;
}

.risk-item.high-risk .risk-indicator {
    background: #e74c3c;
}

.risk-item.medium-risk .risk-indicator {
    background: #f39c12;
}

.risk-item.low-risk .risk-indicator {
    background: #27ae60;
}

.risk-content {
    flex: 1;
}

.risk-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.risk-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.5;
}

.risk-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 12px;
}

.risk-level {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.risk-level.high {
    background: #ffebee;
    color: #c62828;
}

.risk-level.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.risk-level.low {
    background: #e8f5e8;
    color: #2e7d32;
}

.risk-area {
    background: #f8f9fa;
    color: #667eea;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.risk-date {
    color: #999;
}

.risk-assessment {
    margin-bottom: 15px;
}

.assessment-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.assessment-label {
    font-size: 13px;
    color: #666;
    min-width: 60px;
}

.assessment-bar {
    flex: 1;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.assessment-fill {
    height: 100%;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-radius: 3px;
    transition: width 0.3s;
}

.assessment-value {
    font-size: 12px;
    color: #333;
    font-weight: 600;
    min-width: 40px;
}

.risk-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.action-deadline {
    font-size: 13px;
    color: #666;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

/* 按钮样式 */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-info {
    background: #3498db;
    color: white;
}

.btn-info:hover {
    background: #2980b9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .risk-stats {
        grid-template-columns: 1fr;
    }
    
    .list-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .list-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .risk-item {
        flex-direction: column;
        gap: 15px;
    }
    
    .risk-actions {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .action-buttons {
        justify-content: center;
    }
}

/* 应急预案模拟 */
.emergency-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.emergency-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.emergency-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.emergency-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.emergency-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.emergency-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.emergency-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.emergency-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.emergency-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.emergency-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.emergency-plans {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.plans-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.plans-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.plans-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.plan-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.plan-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.plan-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.plan-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.plan-icon.fire {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.plan-icon.chemical {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.plan-icon.medical {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.plan-info {
    flex: 1;
}

.plan-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.plan-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.plan-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.plan-status.active {
    background: #d4edda;
    color: #155724;
}

.plan-details {
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 13px;
    color: #666;
}

.detail-value {
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

.plan-actions {
    display: flex;
    gap: 8px;
}

/* 安全培训矩阵 */
.training-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.training-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.training-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.training-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.training-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.training-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.training-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.training-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.training-stat .stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.stat-progress {
    margin-top: 8px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    border-radius: 3px;
    transition: width 0.3s;
}

.stat-progress span {
    font-size: 12px;
    color: #666;
}

.training-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.training-matrix {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.matrix-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.matrix-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.matrix-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.department-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.matrix-table {
    overflow-x: auto;
}

.matrix-table table {
    width: 100%;
    border-collapse: collapse;
}

.matrix-table th,
.matrix-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.matrix-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.position-cell {
    min-width: 120px;
}

.position-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.position-info strong {
    font-size: 14px;
    color: #333;
}

.position-info span {
    font-size: 12px;
    color: #666;
}

.training-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    display: inline-block;
}

.training-status.completed {
    background: #d4edda;
    color: #155724;
}

.training-status.in-progress {
    background: #fff3cd;
    color: #856404;
}

.training-status.pending {
    background: #f8d7da;
    color: #721c24;
}

.training-status.not-required {
    background: #f8f9fa;
    color: #6c757d;
}

.completion-bar {
    width: 60px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.completion-fill {
    height: 100%;
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    border-radius: 3px;
    transition: width 0.3s;
}

.completion-text {
    font-size: 12px;
    color: #666;
    font-weight: 600;
}

/* 事故案例库 */
.cases-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.cases-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.cases-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cases-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.case-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.case-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.case-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.case-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.case-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.case-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.cases-library {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.library-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.library-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.library-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.case-type-filter,
.severity-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.cases-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.case-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.case-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.case-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.case-type {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
}

.case-type.fire {
    color: #e74c3c;
}

.case-type.electrical {
    color: #f39c12;
}

.case-severity {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.case-severity.major {
    background: #ffebee;
    color: #c62828;
}

.case-severity.general {
    background: #fff3e0;
    color: #ef6c00;
}

.case-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.case-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.case-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
}

.detail-section h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.detail-section ul {
    margin: 0;
    padding-left: 20px;
}

.detail-section li {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.4;
}

.case-meta {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
}

.case-actions {
    display: flex;
    gap: 8px;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .emergency-stats,
    .training-stats,
    .cases-stats {
        grid-template-columns: 1fr;
    }

    .plans-grid {
        grid-template-columns: 1fr;
    }

    .matrix-header,
    .library-header {
        flex-direction: column;
        gap: 15px;
    }

    .matrix-controls,
    .library-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .case-details {
        grid-template-columns: 1fr;
    }

    .case-meta {
        flex-direction: column;
        gap: 8px;
    }
}

/* 安全行为画像 */
.behavior-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.behavior-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.behavior-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.behavior-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.behavior-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.behavior-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.behavior-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.behavior-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.behavior-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.behavior-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.behavior-analysis {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.analysis-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-section h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.chart-container {
    height: 250px;
    position: relative;
}

.employee-profiles {
    margin-top: 30px;
}

.profiles-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.profiles-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.profile-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.employee-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.profiles-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.profile-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.profile-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.profile-item.warning {
    border-left: 4px solid #f39c12;
}

.profile-avatar {
    flex-shrink: 0;
}

.profile-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-info {
    flex: 1;
}

.profile-info h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.profile-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.profile-score {
    text-align: center;
    margin: 0 20px;
}

.score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin: 0 auto 8px;
    position: relative;
}

.score-circle.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.score-value {
    font-size: 18px;
    font-weight: 700;
}

.score-label {
    font-size: 12px;
    color: #666;
}

.profile-metrics {
    display: flex;
    gap: 20px;
}

.metric {
    text-align: center;
    min-width: 80px;
}

.metric-label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.metric-value {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.profile-actions {
    flex-shrink: 0;
}

/* 额外的行为画像响应式样式 */
@media (max-width: 768px) {
    .behavior-stats {
        grid-template-columns: 1fr;
    }

    .analysis-charts {
        grid-template-columns: 1fr;
    }

    .profiles-header {
        flex-direction: column;
        gap: 15px;
    }

    .profile-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .profile-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 15px;
    }

    .profile-metrics {
        justify-content: center;
    }
}
