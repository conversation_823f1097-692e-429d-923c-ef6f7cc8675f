/* 水电气管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 能耗概览 */
.utilities-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-card.electricity::before {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.overview-card.water::before {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.overview-card.gas::before {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.overview-card.carbon::before {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.overview-card.electricity .card-icon {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.overview-card.water .card-icon {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.overview-card.gas .card-icon {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.overview-card.carbon .card-icon {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: baseline;
    gap: 4px;
}

.card-number .unit {
    font-size: 14px;
    font-weight: 500;
    color: #666;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.card-trend.up {
    color: #e74c3c;
}

.card-trend.down {
    color: #27ae60;
}

.card-trend.stable {
    color: #95a5a6;
}

.card-cost {
    font-size: 13px;
    color: #666;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 能耗监测 */
.monitoring-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.real-time-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.chart-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.chart-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.time-range-select,
.energy-type-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-container {
    height: 300px;
    position: relative;
}

.energy-statistics {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.energy-statistics h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.stat-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.stat-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.stat-icon.electricity {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.stat-icon.water {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.stat-icon.gas {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.stat-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.stat-comparison {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.comparison-label {
    font-size: 13px;
    color: #666;
}

.comparison-value {
    font-size: 13px;
    font-weight: 600;
}

.comparison-value.increase {
    color: #e74c3c;
}

.comparison-value.decrease {
    color: #27ae60;
}

.comparison-value.stable {
    color: #95a5a6;
}

.stat-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stat-details span {
    font-size: 12px;
    color: #999;
}

/* 智能表计 */
.meters-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.meters-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.stat-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.stat-card .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.stat-card .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-card .stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.stat-card .stat-rate {
    font-size: 12px;
    color: #27ae60;
    font-weight: 600;
}

.meters-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-select,
.status-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.meters-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.meter-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.meter-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.meter-card.online {
    border-left: 4px solid #27ae60;
}

.meter-card.offline {
    border-left: 4px solid #e74c3c;
}

.meter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.meter-type {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
}

.meter-type.electricity {
    color: #f39c12;
}

.meter-type.water {
    color: #3498db;
}

.meter-type.gas {
    color: #e74c3c;
}

.meter-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.meter-status.online {
    color: #27ae60;
}

.meter-status.offline {
    color: #e74c3c;
}

.meter-info {
    margin-bottom: 15px;
}

.meter-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.meter-info p {
    font-size: 13px;
    color: #666;
    margin-bottom: 2px;
}

.meter-data {
    margin-bottom: 15px;
}

.data-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.data-item:last-child {
    border-bottom: none;
}

.data-label {
    font-size: 13px;
    color: #666;
}

.data-value {
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

.meter-actions {
    display: flex;
    gap: 8px;
}

/* 按钮样式 */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .real-time-charts {
        grid-template-columns: 1fr;
    }
    
    .stats-grid,
    .meters-grid {
        grid-template-columns: 1fr;
    }
    
    .list-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .list-controls {
        flex-direction: column;
        align-items: stretch;
    }
}

/* 异常预警 */
.alerts-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.alert-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.alert-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.alert-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s;
}

.alert-stat.high {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border: 1px solid #f8bbd9;
}

.alert-stat.medium {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border: 1px solid #ffcc80;
}

.alert-stat.low {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border: 1px solid #a5d6a7;
}

.alert-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.alert-stat.high .stat-icon {
    background: #e74c3c;
}

.alert-stat.medium .stat-icon {
    background: #f39c12;
}

.alert-stat.low .stat-icon {
    background: #27ae60;
}

.alert-stat .stat-content h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
}

.alert-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.alert-stat .stat-content p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.alerts-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.alerts-list .list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alerts-list .list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-controls {
    display: flex;
    gap: 12px;
}

.alert-filter,
.energy-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.alerts-container {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.alert-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    position: relative;
    transition: all 0.3s;
}

.alert-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-item.high-risk {
    border-left: 4px solid #e74c3c;
}

.alert-item.medium-risk {
    border-left: 4px solid #f39c12;
}

.alert-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
    margin-top: 4px;
}

.alert-item.high-risk .alert-indicator {
    background: #e74c3c;
}

.alert-item.medium-risk .alert-indicator {
    background: #f39c12;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.alert-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.5;
}

.alert-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 12px;
}

.alert-level {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.alert-level.high {
    background: #ffebee;
    color: #c62828;
}

.alert-level.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.alert-type {
    background: #f8f9fa;
    color: #667eea;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.alert-time {
    color: #999;
}

.alert-suggestions {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-top: 10px;
}

.alert-suggestions h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.alert-suggestions ul {
    margin: 0;
    padding-left: 20px;
}

.alert-suggestions li {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.4;
}

.alert-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* 节能优化 */
.optimization-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.optimization-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.optimization-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.optimization-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.opt-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.opt-stat:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.opt-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.opt-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.opt-stat .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.opt-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.optimization-recommendations {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.recommendations-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recommendations-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recommendations-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.recommendation-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.recommendation-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.recommendation-item.high-priority {
    border-left: 4px solid #e74c3c;
}

.recommendation-item.medium-priority {
    border-left: 4px solid #f39c12;
}

.rec-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.rec-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.rec-info {
    flex: 1;
}

.rec-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.rec-info p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.rec-priority {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.rec-priority.high {
    background: #ffebee;
    color: #c62828;
}

.rec-priority.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.rec-details {
    margin-bottom: 15px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-label {
    font-size: 12px;
    color: #666;
}

.detail-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.rec-benefits {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
}

.rec-benefits h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.rec-benefits ul {
    margin: 0;
    padding-left: 20px;
}

.rec-benefits li {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.4;
}

.rec-actions {
    display: flex;
    gap: 8px;
}

/* 碳排放核算 */
.carbon-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.carbon-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.carbon-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.carbon-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.carbon-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.carbon-stat:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.carbon-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.carbon-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.carbon-stat .stat-value {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.carbon-stat .stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.carbon-stat .stat-trend.decrease {
    color: #27ae60;
}

.stat-progress {
    margin-top: 8px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    border-radius: 3px;
    transition: width 0.3s;
}

.stat-progress span {
    font-size: 12px;
    color: #666;
}

.carbon-analysis {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.analysis-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.analysis-charts .chart-section h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.carbon-factors h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.factors-table {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.factor-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    transition: all 0.3s;
}

.factor-item:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.factor-type {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
}

.factor-value {
    font-size: 14px;
    font-weight: 600;
    color: #27ae60;
}

.factor-source {
    font-size: 12px;
    color: #666;
}

.carbon-report {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.report-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.report-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.report-period {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.report-summary {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.summary-item {
    text-align: center;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
}

.summary-item h5 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.summary-item p {
    font-size: 16px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .alert-stats,
    .optimization-stats,
    .carbon-stats {
        grid-template-columns: 1fr;
    }

    .analysis-charts {
        grid-template-columns: 1fr;
    }

    .alert-item,
    .recommendation-item {
        flex-direction: column;
        gap: 15px;
    }

    .alert-actions,
    .rec-actions {
        flex-direction: row;
        justify-content: center;
    }

    .detail-grid {
        grid-template-columns: 1fr 1fr;
    }

    .factor-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .report-summary {
        grid-template-columns: 1fr 1fr;
    }
}
