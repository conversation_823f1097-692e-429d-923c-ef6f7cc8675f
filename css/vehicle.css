/* 用车管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 车辆概览 */
.vehicle-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    overflow: hidden;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    flex-shrink: 0;
}

.card-icon.success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
}

.card-icon.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.card-icon.info {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-number .unit {
    font-size: 16px;
    color: #999;
    font-weight: 400;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
}

.card-trend.up {
    color: #27ae60;
}

.card-trend.down {
    color: #e74c3c;
}

.card-trend.stable {
    color: #95a5a6;
}

.card-detail {
    font-size: 13px;
    color: #666;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.tab-btn.active {
    background: #3498db;
    color: white;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 智能调度 */
.dispatch-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.dispatch-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.dispatch-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.dispatch-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.dispatch-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.dispatch-stat:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.dispatch-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.dispatch-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.dispatch-stat .stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.stat-progress {
    margin-top: 8px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border-radius: 3px;
    transition: width 0.3s;
}

.dispatch-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

/* 用车申请 */
.dispatch-requests {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.requests-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.requests-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.requests-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.priority-filter,
.status-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.requests-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.request-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.request-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.request-item.urgent {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.request-item.normal {
    border-left: 4px solid #3498db;
}

.request-indicator {
    display: flex;
    align-items: flex-start;
    margin-top: 5px;
}

.priority-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.priority-badge.urgent {
    background: #ffebee;
    color: #c62828;
}

.priority-badge.normal {
    background: #e3f2fd;
    color: #1976d2;
}

.request-content {
    flex: 1;
}

.request-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.request-content > p {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.request-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
}

.detail-label {
    color: #666;
}

.detail-value {
    font-weight: 600;
    color: #333;
}

.ai-recommendation {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.ai-recommendation h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.recommendation-item {
    font-size: 13px;
}

.recommendation-item > div {
    margin-bottom: 6px;
}

.score {
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.score.high {
    background: #e8f5e8;
    color: #2e7d32;
}

.score.medium {
    background: #fff3e0;
    color: #ef6c00;
}

.request-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-start;
}

/* 调度地图 */
.dispatch-map {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.dispatch-map h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.map-container {
    height: 300px;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.map-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 18px;
}

.map-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #3498db;
}

.map-legend {
    position: absolute;
    top: 15px;
    right: 15px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 12px;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-color.available {
    background: #27ae60;
}

.legend-color.busy {
    background: #f39c12;
}

.legend-color.maintenance {
    background: #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .dispatch-stats {
        grid-template-columns: 1fr;
    }
    
    .requests-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .requests-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .request-item {
        flex-direction: column;
        gap: 15px;
    }
    
    .request-actions {
        flex-direction: row;
        justify-content: center;
    }
    
    .request-details {
        grid-template-columns: 1fr;
    }
}

/* 行车安全监测 */
.safety-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.safety-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.safety-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.safety-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.safety-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.safety-stat:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.safety-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.safety-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.safety-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.safety-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.safety-monitoring {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.monitoring-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.monitoring-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.monitoring-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.vehicle-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.monitoring-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.vehicle-monitor {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.vehicle-monitor:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vehicle-monitor.normal {
    border-left: 4px solid #27ae60;
}

.vehicle-monitor.warning {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.monitor-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-indicator.normal {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-indicator.warning {
    background: #fff3e0;
    color: #ef6c00;
}

.monitor-data {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.data-item {
    display: flex;
    justify-content: space-between;
    font-size: 13px;
}

.data-label {
    color: #666;
}

.data-value {
    font-weight: 600;
    color: #333;
}

.data-value.warning {
    color: #f39c12;
}

.safety-indicators {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    padding: 6px 10px;
    border-radius: 6px;
}

.indicator.safe {
    background: #e8f5e8;
    color: #2e7d32;
}

.indicator.warning {
    background: #fff3e0;
    color: #ef6c00;
}

.monitor-actions {
    display: flex;
    gap: 8px;
}

.safety-alerts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.alerts-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.alert-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.alert-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-item.high {
    border-left: 4px solid #e74c3c;
}

.alert-item.medium {
    border-left: 4px solid #f39c12;
}

.alert-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    flex-shrink: 0;
}

.alert-item.high .alert-icon {
    background: #ffebee;
    color: #e74c3c;
}

.alert-item.medium .alert-icon {
    background: #fff3e0;
    color: #f39c12;
}

.alert-content {
    flex: 1;
}

.alert-content h5 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
}

.alert-content p {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
}

.alert-time {
    font-size: 11px;
    color: #999;
}

.alert-actions {
    display: flex;
    align-items: center;
}

/* 费用智能核算 */
.cost-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.cost-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.cost-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cost-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.cost-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.cost-stat:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.cost-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.cost-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.cost-stat .stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.cost-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.cost-analysis {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.analysis-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.chart-section h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.chart-container {
    height: 250px;
    position: relative;
}

.cost-allocation {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.allocation-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.allocation-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.allocation-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.department-filter,
.month-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.allocation-table {
    overflow-x: auto;
}

.allocation-table table {
    width: 100%;
    border-collapse: collapse;
}

.allocation-table th,
.allocation-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.allocation-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.department-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.department-info strong {
    font-size: 14px;
    color: #333;
}

.department-info span {
    font-size: 12px;
    color: #666;
}

.total-cost {
    font-weight: 700;
    color: #e74c3c;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .safety-stats,
    .cost-stats {
        grid-template-columns: 1fr;
    }

    .monitoring-header,
    .allocation-header {
        flex-direction: column;
        gap: 15px;
    }

    .monitoring-controls,
    .allocation-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .monitoring-grid {
        grid-template-columns: 1fr;
    }

    .monitor-data {
        grid-template-columns: 1fr;
    }

    .analysis-charts {
        grid-template-columns: 1fr;
    }
}

/* 车辆健康档案 */
.health-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.health-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.health-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.health-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.health-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.health-stat:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.health-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.health-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.health-stat .stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.health-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.vehicle-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.health-filter,
.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.search-input {
    min-width: 200px;
}

.vehicles-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.vehicle-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.vehicle-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vehicle-card.healthy {
    border-left: 4px solid #27ae60;
}

.vehicle-card.maintenance {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.vehicle-card.repair {
    border-left: 4px solid #e74c3c;
    background: linear-gradient(135deg, #ffebee 0%, rgba(255, 235, 238, 0.3) 100%);
}

.vehicle-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.vehicle-image {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.vehicle-info {
    flex: 1;
}

.vehicle-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.vehicle-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.health-indicator {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.health-indicator.healthy {
    background: #e8f5e8;
    color: #2e7d32;
}

.health-indicator.maintenance {
    background: #fff3e0;
    color: #ef6c00;
}

.health-indicator.repair {
    background: #ffebee;
    color: #c62828;
}

.vehicle-details {
    margin-bottom: 15px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
    font-size: 13px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    color: #666;
}

.detail-value {
    font-weight: 600;
    color: #333;
}

.detail-value.warning {
    color: #f39c12;
}

.vehicle-actions {
    display: flex;
    gap: 8px;
}

/* 新能源车管理 */
.electric-dashboard {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.electric-overview {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.electric-overview h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.electric-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.electric-stat {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
    background: linear-gradient(135deg, #e8f5e8 0%, rgba(232, 245, 232, 0.3) 100%);
}

.electric-stat:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.electric-stat .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.electric-stat .stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.electric-stat .stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.electric-stat .stat-content p {
    font-size: 12px;
    color: #999;
    margin: 0;
}

.charging-stations {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.stations-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stations-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stations-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.distance-filter {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.stations-list {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.station-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    transition: all 0.3s;
}

.station-item:hover {
    border-color: #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.1);
}

.station-info {
    flex: 1;
}

.station-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.station-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.station-status {
    display: flex;
    align-items: center;
    gap: 15px;
}

.available-count {
    text-align: center;
}

.available-count .count {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #27ae60;
}

.available-count .label {
    font-size: 12px;
    color: #666;
}

.price {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.station-actions {
    display: flex;
    gap: 8px;
}

.electric-vehicles {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.ev-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ev-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.ev-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s;
}

.ev-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ev-card.low-battery {
    border-left: 4px solid #f39c12;
    background: linear-gradient(135deg, #fff3e0 0%, rgba(255, 243, 224, 0.3) 100%);
}

.ev-header-info {
    margin-bottom: 15px;
}

.ev-header-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.ev-header-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.battery-status {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.battery-icon {
    width: 60px;
    height: 30px;
    border: 2px solid #27ae60;
    border-radius: 4px;
    position: relative;
    background: #f8f9fa;
}

.battery-icon.low {
    border-color: #f39c12;
}

.battery-icon::after {
    content: '';
    position: absolute;
    right: -6px;
    top: 8px;
    width: 4px;
    height: 14px;
    background: #27ae60;
    border-radius: 0 2px 2px 0;
}

.battery-icon.low::after {
    background: #f39c12;
}

.battery-level {
    height: 100%;
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    border-radius: 2px;
    transition: width 0.3s;
}

.battery-icon.low .battery-level {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.battery-percentage {
    font-size: 18px;
    font-weight: 700;
    color: #333;
}

.ev-details {
    margin-bottom: 15px;
}

.ev-details .detail-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    font-size: 13px;
}

.ev-details .detail-value.warning {
    color: #f39c12;
}

.ev-actions {
    display: flex;
    gap: 8px;
}

/* 额外的健康档案和新能源车响应式样式 */
@media (max-width: 768px) {
    .health-stats,
    .electric-stats {
        grid-template-columns: 1fr;
    }

    .list-header,
    .stations-header {
        flex-direction: column;
        gap: 15px;
    }

    .list-controls,
    .stations-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .vehicles-grid,
    .ev-grid {
        grid-template-columns: 1fr;
    }

    .station-item {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .station-status {
        justify-content: space-between;
    }

    .station-actions {
        justify-content: center;
    }
}
