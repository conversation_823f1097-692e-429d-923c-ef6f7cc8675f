/* 仓库管理页面样式 */

/* 页面标题 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.page-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

/* 仓库概览 */
.warehouse-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    flex-shrink: 0;
}

.card-icon.warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.card-number {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.card-trend.up {
    color: #27ae60;
}

.card-trend.down {
    color: #e74c3c;
}

.card-trend.stable {
    color: #95a5a6;
}

/* 模块导航 */
.module-navigation {
    margin-bottom: 30px;
}

.module-tabs {
    display: flex;
    gap: 2px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    min-width: 140px;
    padding: 12px 16px;
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 库存预警 */
.inventory-alerts {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    margin-bottom: 30px;
    overflow: hidden;
}

.alerts-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alerts-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-filters {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.alerts-list {
    padding: 20px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.alert-item:last-child {
    border-bottom: none;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.alert-item.critical .alert-icon {
    background: #e74c3c;
}

.alert-item.warning .alert-icon {
    background: #f39c12;
}

.alert-item.excess .alert-icon {
    background: #3498db;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.alert-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.alert-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
}

.alert-actions {
    display: flex;
    gap: 8px;
}

/* 图表区域 */
.inventory-charts,
.analysis-charts {
    margin-bottom: 30px;
}

.chart-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.chart-select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-container {
    height: 300px;
    position: relative;
}

/* 智能建议 */
.inventory-suggestions,
.analysis-recommendations {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.suggestions-header,
.recommendations-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.suggestions-header h3,
.recommendations-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.suggestions-list,
.recommendations-list {
    padding: 20px;
}

.suggestion-item,
.recommendation-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:last-child,
.recommendation-item:last-child {
    border-bottom: none;
}

.suggestion-icon,
.recommendation-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.suggestion-icon.restock,
.recommendation-icon.fast {
    background: #27ae60;
}

.suggestion-icon.clearance,
.recommendation-icon.slow {
    background: #f39c12;
}

.suggestion-content,
.recommendation-content {
    flex: 1;
}

.suggestion-content h4,
.recommendation-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.suggestion-content p,
.recommendation-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.suggestion-priority,
.recommendation-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.suggestion-priority.high,
.recommendation-priority.high {
    background: #ffebee;
    color: #c62828;
}

.suggestion-priority.medium,
.recommendation-priority.medium {
    background: #fff3e0;
    color: #ef6c00;
}

/* 物资追溯 */
.tracking-search {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
    margin-bottom: 30px;
}

.search-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.search-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
}

.search-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.search-btn {
    padding: 10px 20px;
}

.tracking-result {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.result-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.result-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.tracking-timeline {
    padding: 30px;
    position: relative;
}

.tracking-timeline::before {
    content: '';
    position: absolute;
    left: 80px;
    top: 30px;
    bottom: 30px;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    position: relative;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-date {
    width: 60px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-align: center;
    flex-shrink: 0;
}

.timeline-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
}

.timeline-icon.purchase {
    background: #3498db;
}

.timeline-icon.storage {
    background: #9b59b6;
}

.timeline-icon.request {
    background: #f39c12;
}

.timeline-icon.approval {
    background: #27ae60;
}

.timeline-icon.delivery {
    background: #e74c3c;
}

.timeline-item.current .timeline-icon {
    box-shadow: 0 0 0 4px rgba(231, 76, 60, 0.2);
}

.timeline-content {
    flex: 1;
    padding-top: 5px;
}

.timeline-content h5 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.timeline-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

/* 多仓库管理 */
.warehouses-overview {
    margin-bottom: 30px;
}

.warehouses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.warehouse-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 20px;
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
}

.warehouse-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

.warehouse-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.warehouse-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.warehouse-info {
    flex: 1;
}

.warehouse-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.warehouse-info p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.warehouse-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.warehouse-status.active {
    background: #d4edda;
    color: #155724;
}

.warehouse-status.maintenance {
    background: #fff3cd;
    color: #856404;
}

.warehouse-stats {
    padding: 15px 0;
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 15px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.stat-row:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.warehouse-actions {
    display: flex;
    gap: 8px;
}

/* 调拨管理 */
.transfer-management {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.transfer-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.transfer-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.transfer-list {
    padding: 20px;
}

.transfer-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.transfer-item:last-child {
    border-bottom: none;
}

.transfer-info {
    flex: 1;
}

.transfer-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.transfer-info p {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.transfer-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #999;
}

.transfer-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    min-width: 80px;
    text-align: center;
}

.transfer-status.pending {
    background: #fff3cd;
    color: #856404;
}

.transfer-status.approved {
    background: #d4edda;
    color: #155724;
}

.transfer-actions {
    display: flex;
    gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .module-tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        min-width: auto;
    }
    
    .chart-row {
        grid-template-columns: 1fr;
    }
    
    .search-row {
        grid-template-columns: 1fr;
    }
    
    .warehouses-grid {
        grid-template-columns: 1fr;
    }
    
    .alert-item,
    .transfer-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .alert-actions,
    .transfer-actions {
        justify-content: center;
    }
}

/* 领用审批 */
.approval-stats {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.stat-icon.pending {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.stat-icon.approved {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.stat-icon.rejected {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.stat-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.approval-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.list-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-filters {
    display: flex;
    gap: 12px;
    align-items: center;
}

.approval-table {
    overflow-x: auto;
}

.approval-table table {
    width: 100%;
    border-collapse: collapse;
}

.approval-table th,
.approval-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.approval-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.approval-table td {
    font-size: 14px;
    color: #666;
}

.approval-table tr:hover {
    background: #f8f9fa;
}

.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.status.pending {
    background: #fff3cd;
    color: #856404;
}

.status.approved {
    background: #d4edda;
    color: #155724;
}

.status.rejected {
    background: #f8d7da;
    color: #721c24;
}

/* 周转分析 */
.analysis-overview {
    margin-bottom: 30px;
}

.kpi-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.kpi-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.kpi-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.kpi-content {
    flex: 1;
}

.kpi-content h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.kpi-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.kpi-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.kpi-trend.up {
    color: #27ae60;
}

.kpi-trend.down {
    color: #e74c3c;
}

.kpi-trend.stable {
    color: #95a5a6;
}

/* 按钮样式 */
.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

/* 额外的响应式样式 */
@media (max-width: 768px) {
    .stats-grid,
    .kpi-cards {
        grid-template-columns: 1fr;
    }

    .list-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .list-filters {
        justify-content: space-between;
    }

    .approval-table {
        font-size: 12px;
    }

    .approval-table th,
    .approval-table td {
        padding: 10px 8px;
    }
}
