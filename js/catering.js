// 餐饮管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化菜单切换
    initMenuToggle();
    
    // 初始化标签页
    initTabs();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
});

// 初始化菜单切换功能
function initMenuToggle() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
        });
    }
}

// 初始化标签页
function initTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

// 初始化图表
function initCharts() {
    // 就餐人数趋势图表
    initDiningTrendChart();
    
    // 菜品销售分布图表
    initDishSalesChart();
    
    // 营养成分分析图表
    initNutritionChart();
}

// 就餐人数趋势图表
function initDiningTrendChart() {
    const ctx = document.getElementById('diningTrendChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [{
                label: '就餐人数',
                data: [1650, 1780, 1920, 1856, 1945, 1200, 980],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '预计人数',
                data: [1700, 1800, 1900, 1850, 1950, 1300, 1000],
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                tension: 0.4,
                fill: false,
                borderDash: [5, 5]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 菜品销售分布图表
function initDishSalesChart() {
    const ctx = document.getElementById('dishSalesChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['红烧肉', '蔬菜沙拉', '蒸蛋羹', '宫保鸡丁', '青椒土豆丝', '其他'],
            datasets: [{
                data: [22, 18, 15, 12, 10, 23],
                backgroundColor: [
                    '#667eea',
                    '#f093fb',
                    '#ffeaa7',
                    '#fd79a8',
                    '#a29bfe',
                    '#6c5ce7'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

// 营养成分分析图表
function initNutritionChart() {
    const ctx = document.getElementById('nutritionChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['蛋白质', '碳水化合物', '脂肪', '维生素', '纤维', '矿物质'],
            datasets: [{
                label: '当前营养配比',
                data: [85, 75, 60, 90, 70, 80],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.2)',
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: '#667eea'
            }, {
                label: '推荐营养配比',
                data: [80, 70, 50, 95, 85, 75],
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.2)',
                pointBackgroundColor: '#27ae60',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: '#27ae60'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建菜品按钮
    const newMenuBtn = document.getElementById('newMenuBtn');
    if (newMenuBtn) {
        newMenuBtn.addEventListener('click', function() {
            showModal('新建菜品', createNewMenuForm());
        });
    }
    
    // 运营报表按钮
    const reportBtn = document.getElementById('reportBtn');
    if (reportBtn) {
        reportBtn.addEventListener('click', function() {
            generateOperationReport();
        });
    }
    
    // 新建采购按钮
    const newPurchaseBtn = document.getElementById('newPurchaseBtn');
    if (newPurchaseBtn) {
        newPurchaseBtn.addEventListener('click', function() {
            showModal('新建采购', createNewPurchaseForm());
        });
    }
    
    // 库存盘点按钮
    const inventoryCheckBtn = document.getElementById('inventoryCheckBtn');
    if (inventoryCheckBtn) {
        inventoryCheckBtn.addEventListener('click', function() {
            showModal('库存盘点', createInventoryCheckForm());
        });
    }
    
    // 新增供应商按钮
    const newSupplierBtn = document.getElementById('newSupplierBtn');
    if (newSupplierBtn) {
        newSupplierBtn.addEventListener('click', function() {
            showModal('新增供应商', createNewSupplierForm());
        });
    }
    
    // 新增菜品按钮
    const addDishBtn = document.getElementById('addDishBtn');
    if (addDishBtn) {
        addDishBtn.addEventListener('click', function() {
            showModal('新增菜品', createNewDishForm());
        });
    }
    
    // 营养分析按钮
    const nutritionAnalysisBtn = document.getElementById('nutritionAnalysisBtn');
    if (nutritionAnalysisBtn) {
        nutritionAnalysisBtn.addEventListener('click', function() {
            showModal('营养分析报告', createNutritionAnalysisForm());
        });
    }
}

// 显示模态框
function showModal(title, content) {
    // 创建模态框HTML
    const modalHTML = `
        <div class="modal-overlay" id="modalOverlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button class="btn btn-primary" onclick="submitModal()">确定</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 显示模态框
    document.getElementById('modalOverlay').style.display = 'flex';
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('modalOverlay');
    if (modal) {
        modal.remove();
    }
}

// 提交模态框
function submitModal() {
    // 这里可以添加具体的提交逻辑
    alert('操作成功！');
    closeModal();
}

// 创建新建菜品表单
function createNewMenuForm() {
    return `
        <div class="form-container">
            <div class="form-row">
                <div class="form-group">
                    <label>菜品名称</label>
                    <input type="text" class="form-control" placeholder="请输入菜品名称">
                </div>
                <div class="form-group">
                    <label>菜品分类</label>
                    <select class="form-control">
                        <option>荤菜</option>
                        <option>素菜</option>
                        <option>汤类</option>
                        <option>主食</option>
                        <option>小食</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>售价</label>
                    <input type="number" class="form-control" placeholder="请输入售价">
                </div>
                <div class="form-group">
                    <label>成本</label>
                    <input type="number" class="form-control" placeholder="请输入成本">
                </div>
            </div>
            <div class="form-group">
                <label>菜品描述</label>
                <textarea class="form-control" rows="3" placeholder="请输入菜品描述"></textarea>
            </div>
            <div class="form-group">
                <label>菜品图片</label>
                <input type="file" class="form-control" accept="image/*">
            </div>
        </div>
    `;
}

// 创建新建采购表单
function createNewPurchaseForm() {
    return `
        <div class="form-container">
            <div class="form-group">
                <label>供应商</label>
                <select class="form-control">
                    <option>绿色农场有限公司</option>
                    <option>新鲜肉类批发市场</option>
                    <option>优质调料供应商</option>
                </select>
            </div>
            <div class="form-group">
                <label>采购清单</label>
                <div class="purchase-items">
                    <div class="purchase-item">
                        <input type="text" placeholder="食材名称" class="form-control">
                        <input type="number" placeholder="数量" class="form-control">
                        <input type="number" placeholder="单价" class="form-control">
                    </div>
                </div>
                <button type="button" class="btn btn-sm btn-secondary">添加食材</button>
            </div>
            <div class="form-group">
                <label>预计到货时间</label>
                <input type="datetime-local" class="form-control">
            </div>
        </div>
    `;
}

// 创建库存盘点表单
function createInventoryCheckForm() {
    return `
        <div class="form-container">
            <div class="form-group">
                <label>盘点类型</label>
                <select class="form-control">
                    <option>全面盘点</option>
                    <option>分类盘点</option>
                    <option>重点盘点</option>
                </select>
            </div>
            <div class="form-group">
                <label>盘点范围</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" checked> 蔬菜类</label>
                    <label><input type="checkbox" checked> 肉类</label>
                    <label><input type="checkbox" checked> 调料类</label>
                    <label><input type="checkbox" checked> 主食类</label>
                </div>
            </div>
            <div class="form-group">
                <label>盘点时间</label>
                <input type="datetime-local" class="form-control">
            </div>
            <div class="form-group">
                <label>盘点人员</label>
                <input type="text" class="form-control" placeholder="请输入盘点人员">
            </div>
        </div>
    `;
}

// 创建新增供应商表单
function createNewSupplierForm() {
    return `
        <div class="form-container">
            <div class="form-row">
                <div class="form-group">
                    <label>供应商名称</label>
                    <input type="text" class="form-control" placeholder="请输入供应商名称">
                </div>
                <div class="form-group">
                    <label>联系人</label>
                    <input type="text" class="form-control" placeholder="请输入联系人">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>联系电话</label>
                    <input type="tel" class="form-control" placeholder="请输入联系电话">
                </div>
                <div class="form-group">
                    <label>主营产品</label>
                    <input type="text" class="form-control" placeholder="如：有机蔬菜、肉类等">
                </div>
            </div>
            <div class="form-group">
                <label>供应商地址</label>
                <textarea class="form-control" rows="2" placeholder="请输入详细地址"></textarea>
            </div>
            <div class="form-group">
                <label>资质证明</label>
                <input type="file" class="form-control" multiple accept=".pdf,.jpg,.png">
            </div>
        </div>
    `;
}

// 创建新增菜品表单
function createNewDishForm() {
    return createNewMenuForm(); // 复用新建菜品表单
}

// 创建营养分析表单
function createNutritionAnalysisForm() {
    return `
        <div class="nutrition-report">
            <h4>本周营养分析报告</h4>
            <div class="nutrition-summary">
                <div class="nutrition-item">
                    <span class="label">平均热量:</span>
                    <span class="value">2,150卡/天</span>
                    <span class="status normal">正常</span>
                </div>
                <div class="nutrition-item">
                    <span class="label">蛋白质摄入:</span>
                    <span class="value">85g/天</span>
                    <span class="status good">良好</span>
                </div>
                <div class="nutrition-item">
                    <span class="label">维生素含量:</span>
                    <span class="value">90%</span>
                    <span class="status excellent">优秀</span>
                </div>
                <div class="nutrition-item">
                    <span class="label">纤维摄入:</span>
                    <span class="value">25g/天</span>
                    <span class="status low">偏低</span>
                </div>
            </div>
            <div class="nutrition-suggestions">
                <h5>改进建议:</h5>
                <ul>
                    <li>增加绿叶蔬菜的供应量，提高纤维摄入</li>
                    <li>适当减少油炸食品，控制脂肪含量</li>
                    <li>增加豆类制品，提供优质植物蛋白</li>
                </ul>
            </div>
        </div>
    `;
}

// 生成运营报表
function generateOperationReport() {
    // 模拟生成报表
    const reportData = {
        generateTime: new Date().toLocaleString(),
        dailyDiners: 1856,
        dailyRevenue: '¥28,560',
        satisfaction: '4.6分',
        inventoryAlerts: 12
    };
    
    console.log('生成运营报表数据:', reportData);
    alert('运营报表生成成功！');
}
