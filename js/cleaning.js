// 保洁管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化筛选功能
    initFilters();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'schedule':
                    initScheduleTab();
                    break;
                case 'quality':
                    initQualityTab();
                    break;
                case 'supplies':
                    initSuppliesTab();
                    break;
                case 'disinfection':
                    initDisinfectionTab();
                    break;
                case 'green':
                    initGreenTab();
                    break;
            }
        });
    });
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建任务
    const newTaskBtn = document.getElementById('newTaskBtn');
    if (newTaskBtn) {
        newTaskBtn.addEventListener('click', function() {
            showTaskForm();
        });
    }

    // 新建巡检
    const newInspectionBtn = document.getElementById('newInspectionBtn');
    if (newInspectionBtn) {
        newInspectionBtn.addEventListener('click', function() {
            showInspectionForm();
        });
    }

    // 添加耗材
    const addSupplyBtn = document.getElementById('addSupplyBtn');
    if (addSupplyBtn) {
        addSupplyBtn.addEventListener('click', function() {
            showSupplyForm();
        });
    }

    // 新建消杀任务
    const newDisinfectionBtn = document.getElementById('newDisinfectionBtn');
    if (newDisinfectionBtn) {
        newDisinfectionBtn.addEventListener('click', function() {
            showDisinfectionForm();
        });
    }

    // 推荐环保产品
    const addEcoProductBtn = document.getElementById('addEcoProductBtn');
    if (addEcoProductBtn) {
        addEcoProductBtn.addEventListener('click', function() {
            showEcoProductForm();
        });
    }

    // 排班优化
    const scheduleOptimizeBtn = document.getElementById('scheduleOptimizeBtn');
    if (scheduleOptimizeBtn) {
        scheduleOptimizeBtn.addEventListener('click', function() {
            optimizeSchedule();
        });
    }

    // 保洁报告
    const cleaningReportBtn = document.getElementById('cleaningReportBtn');
    if (cleaningReportBtn) {
        cleaningReportBtn.addEventListener('click', function() {
            generateCleaningReport();
        });
    }

    // 智能分配
    const autoAssignBtn = document.getElementById('autoAssignBtn');
    if (autoAssignBtn) {
        autoAssignBtn.addEventListener('click', function() {
            runAutoAssignment();
        });
    }

    // 导出排班表
    const exportScheduleBtn = document.getElementById('exportScheduleBtn');
    if (exportScheduleBtn) {
        exportScheduleBtn.addEventListener('click', function() {
            exportSchedule();
        });
    }

    // 任务操作按钮
    const taskActionBtns = document.querySelectorAll('.task-actions .btn');
    taskActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const taskCard = this.closest('.task-card');
            const taskTitle = taskCard.querySelector('h4').textContent;
            handleTaskAction(action, taskTitle);
        });
    });

    // 员工操作按钮
    const staffActionBtns = document.querySelectorAll('.schedule-table .btn');
    staffActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const row = this.closest('tr');
            const staffName = row.querySelector('strong').textContent;
            handleStaffAction(action, staffName);
        });
    });
}

// 显示任务表单
function showTaskForm() {
    const modal = createModal('新建保洁任务', `
        <form class="task-form">
            <div class="form-row">
                <div class="form-group">
                    <label>任务名称 *</label>
                    <input type="text" name="taskName" required placeholder="请输入任务名称">
                </div>
                <div class="form-group">
                    <label>优先级 *</label>
                    <select name="priority" required>
                        <option value="">请选择优先级</option>
                        <option value="urgent">紧急</option>
                        <option value="high">高</option>
                        <option value="normal">普通</option>
                        <option value="low">低</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>清洁区域 *</label>
                    <select name="area" required>
                        <option value="">请选择区域</option>
                        <option value="surgery">手术室</option>
                        <option value="icu">ICU</option>
                        <option value="outpatient">门诊大厅</option>
                        <option value="ward">病房楼</option>
                        <option value="office">办公楼</option>
                        <option value="public">公共区域</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>预计时间 *</label>
                    <input type="number" name="estimatedTime" required placeholder="小时" min="0.5" step="0.5">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>开始时间 *</label>
                    <input type="datetime-local" name="startTime" required>
                </div>
                <div class="form-group">
                    <label>截止时间 *</label>
                    <input type="datetime-local" name="deadline" required>
                </div>
            </div>
            <div class="form-group">
                <label>清洁要求 *</label>
                <textarea name="requirements" required placeholder="请详细描述清洁要求和标准"></textarea>
            </div>
            <div class="form-group">
                <label>指定人员</label>
                <select name="assignedStaff">
                    <option value="">系统自动分配</option>
                    <option value="li">李阿姨</option>
                    <option value="wang">王师傅</option>
                    <option value="zhang">张阿姨</option>
                    <option value="liu">刘师傅</option>
                </select>
            </div>
            <div class="form-group">
                <label>备注</label>
                <textarea name="notes" placeholder="请输入备注信息"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">创建任务</button>
            </div>
        </form>
    `);

    modal.querySelector('.task-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const taskName = e.target.taskName.value;
        Utils.showMessage(`保洁任务"${taskName}"创建成功`, 'success');
        closeModal(modal);
    });
}

// 排班优化
function optimizeSchedule() {
    Utils.showMessage('正在运行智能排班优化算法...', 'info');
    setTimeout(() => {
        Utils.showMessage('排班优化完成，已生成最优排班方案', 'success');
        // 更新排班显示
        updateScheduleDisplay();
    }, 3000);
}

// 更新排班显示
function updateScheduleDisplay() {
    const scheduleRows = document.querySelectorAll('.schedule-table tbody tr');
    scheduleRows.forEach(row => {
        const progressBar = row.querySelector('.progress-mini .progress-fill');
        if (progressBar && Math.random() > 0.5) {
            // 随机更新一些进度
            const newProgress = Math.floor(Math.random() * 40) + 60; // 60-100%
            progressBar.style.width = newProgress + '%';
            row.querySelector('.progress-mini').nextElementSibling.textContent = newProgress + '%';
        }
    });
}

// 生成保洁报告
function generateCleaningReport() {
    Utils.showMessage('正在生成保洁管理报告...', 'info');
    setTimeout(() => {
        Utils.showMessage('保洁管理报告生成成功', 'success');
    }, 2000);
}

// 运行智能分配
function runAutoAssignment() {
    Utils.showMessage('正在运行AI智能任务分配...', 'info');
    setTimeout(() => {
        Utils.showMessage('智能分配完成，已优化任务分配方案', 'success');
        // 更新推荐方案
        updateRecommendations();
    }, 3000);
}

// 更新推荐方案
function updateRecommendations() {
    const recommendations = document.querySelectorAll('.ai-recommendation');
    recommendations.forEach(rec => {
        const scoreElement = rec.querySelector('.score');
        if (scoreElement) {
            const currentScore = parseInt(scoreElement.textContent);
            const newScore = Math.min(100, currentScore + Math.floor(Math.random() * 10));
            scoreElement.textContent = newScore + '分';
            
            // 更新评分颜色
            scoreElement.className = 'score ' + (newScore >= 90 ? 'high' : 'medium');
        }
    });
}

// 导出排班表
function exportSchedule() {
    Utils.showMessage('正在导出排班表...', 'info');
    setTimeout(() => {
        Utils.showMessage('排班表导出成功，已下载Excel文件', 'success');
    }, 1500);
}

// 处理任务操作
function handleTaskAction(action, taskTitle) {
    switch(action) {
        case '立即分配':
            Utils.showMessage(`正在立即分配"${taskTitle}"...`, 'info');
            break;
        case '分配任务':
            Utils.showMessage(`正在分配"${taskTitle}"...`, 'info');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看"${taskTitle}"详细信息...`, 'info');
            break;
        case '修改任务':
            Utils.showMessage(`正在修改"${taskTitle}"...`, 'info');
            break;
        case '联系人员':
            Utils.showMessage(`正在联系"${taskTitle}"执行人员...`, 'info');
            break;
        case '查看进度':
            Utils.showMessage(`正在查看"${taskTitle}"执行进度...`, 'info');
            break;
    }
}

// 处理员工操作
function handleStaffAction(action, staffName) {
    switch(action) {
        case '查看详情':
            Utils.showMessage(`正在查看${staffName}的详细信息...`, 'info');
            break;
    }
}

// 初始化筛选功能
function initFilters() {
    // 区域筛选
    const areaFilters = document.querySelectorAll('.area-filter');
    areaFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByArea(filterValue);
        });
    });

    // 优先级筛选
    const priorityFilters = document.querySelectorAll('.priority-filter');
    priorityFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByPriority(filterValue);
        });
    });

    // 班次筛选
    const shiftFilters = document.querySelectorAll('.shift-filter');
    shiftFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByShift(filterValue);
        });
    });
}

// 按区域筛选
function filterByArea(filterValue) {
    const taskCards = document.querySelectorAll('.task-card');
    
    taskCards.forEach(card => {
        if (filterValue === '全部区域') {
            card.style.display = 'block';
        } else {
            const area = card.querySelector('.detail-value').textContent;
            if (area.includes(filterValue)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// 按优先级筛选
function filterByPriority(filterValue) {
    const taskCards = document.querySelectorAll('.task-card');
    
    taskCards.forEach(card => {
        if (filterValue === '全部优先级') {
            card.style.display = 'block';
        } else {
            const priority = card.querySelector('.priority-badge').textContent;
            if (priority.includes(filterValue)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// 按班次筛选
function filterByShift(filterValue) {
    const scheduleRows = document.querySelectorAll('.schedule-table tbody tr');
    
    scheduleRows.forEach(row => {
        if (filterValue === '全部班次') {
            row.style.display = 'table-row';
        } else {
            const shift = row.querySelector('.shift-badge').textContent;
            if (shift.includes(filterValue)) {
                row.style.display = 'table-row';
            } else {
                row.style.display = 'none';
            }
        }
    });
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateCleaningData, 30000);
}

// 更新保洁数据
function updateCleaningData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number && !isNaN(parseInt(number.textContent))) {
            // 随机变化数据
            const currentValue = parseInt(number.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
            const newValue = Math.max(0, currentValue + change);
            
            if (newValue !== currentValue) {
                number.textContent = newValue + (number.textContent.includes('人') ? '人' : 
                                                number.textContent.includes('项') ? '项' : 
                                                number.textContent.includes('分') ? '分' : 
                                                number.textContent.includes('%') ? '%' : '');
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });

    // 更新任务进度
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        if (Math.random() > 0.8) { // 20%概率更新
            const currentWidth = parseInt(bar.style.width) || 0;
            const newWidth = Math.min(100, currentWidth + Math.floor(Math.random() * 10));
            bar.style.width = newWidth + '%';
        }
    });
}

// 标签页初始化函数
function initScheduleTab() {
    // 智能排班标签页初始化
}

function initQualityTab() {
    // 质量评估标签页初始化
}

function initSuppliesTab() {
    // 耗材管理标签页初始化
}

function initDisinfectionTab() {
    // 消杀记录标签页初始化
}

function initGreenTab() {
    // 绿色清洁标签页初始化
}

// 显示巡检表单
function showInspectionForm() {
    const modal = createModal('新建质量巡检', `
        <form class="inspection-form">
            <div class="form-row">
                <div class="form-group">
                    <label>检查区域 *</label>
                    <select name="area" required>
                        <option value="">请选择区域</option>
                        <option value="surgery">手术室</option>
                        <option value="icu">ICU</option>
                        <option value="outpatient">门诊大厅</option>
                        <option value="ward">病房楼</option>
                        <option value="office">办公楼</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>检查员 *</label>
                    <select name="inspector" required>
                        <option value="">请选择检查员</option>
                        <option value="wang">王主管</option>
                        <option value="li">李主管</option>
                        <option value="zhang">张主管</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>检查类型 *</label>
                    <select name="type" required>
                        <option value="">请选择类型</option>
                        <option value="routine">日常检查</option>
                        <option value="deep">深度检查</option>
                        <option value="special">专项检查</option>
                        <option value="emergency">应急检查</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>计划时间 *</label>
                    <input type="datetime-local" name="scheduledTime" required>
                </div>
            </div>
            <div class="form-group">
                <label>检查标准</label>
                <select name="standard">
                    <option value="daily">日常清洁标准</option>
                    <option value="sterile">无菌清洁标准</option>
                    <option value="isolation">隔离病房标准</option>
                    <option value="surgery">手术室标准</option>
                </select>
            </div>
            <div class="form-group">
                <label>检查要点</label>
                <textarea name="checkpoints" placeholder="请输入具体检查要点"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">创建巡检</button>
            </div>
        </form>
    `);

    modal.querySelector('.inspection-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const area = e.target.area.value;
        const inspector = e.target.inspector.value;
        Utils.showMessage(`质量巡检已创建，检查区域: ${area}，检查员: ${inspector}`, 'success');
        closeModal(modal);
    });
}

// 显示耗材表单
function showSupplyForm() {
    const modal = createModal('添加清洁耗材', `
        <form class="supply-form">
            <div class="form-row">
                <div class="form-group">
                    <label>耗材名称 *</label>
                    <input type="text" name="name" required placeholder="请输入耗材名称">
                </div>
                <div class="form-group">
                    <label>耗材类别 *</label>
                    <select name="category" required>
                        <option value="">请选择类别</option>
                        <option value="cleaner">清洁剂</option>
                        <option value="disinfectant">消毒用品</option>
                        <option value="tool">清洁工具</option>
                        <option value="protection">防护用品</option>
                        <option value="bag">垃圾袋</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>品牌</label>
                    <input type="text" name="brand" placeholder="请输入品牌">
                </div>
                <div class="form-group">
                    <label>规格</label>
                    <input type="text" name="specification" placeholder="请输入规格">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>当前库存 *</label>
                    <input type="number" name="currentStock" required min="0" placeholder="请输入当前库存">
                </div>
                <div class="form-group">
                    <label>安全库存 *</label>
                    <input type="number" name="safetyStock" required min="0" placeholder="请输入安全库存">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>单价 *</label>
                    <input type="number" name="price" required min="0" step="0.01" placeholder="请输入单价">
                </div>
                <div class="form-group">
                    <label>供应商</label>
                    <input type="text" name="supplier" placeholder="请输入供应商">
                </div>
            </div>
            <div class="form-group">
                <label>备注</label>
                <textarea name="notes" placeholder="请输入备注信息"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">添加耗材</button>
            </div>
        </form>
    `);

    modal.querySelector('.supply-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const name = e.target.name.value;
        const category = e.target.category.value;
        Utils.showMessage(`耗材"${name}"已添加到${category}类别`, 'success');
        closeModal(modal);
    });
}

// 显示消杀任务表单
function showDisinfectionForm() {
    const modal = createModal('新建消杀任务', `
        <form class="disinfection-form">
            <div class="form-row">
                <div class="form-group">
                    <label>消杀区域 *</label>
                    <select name="area" required>
                        <option value="">请选择区域</option>
                        <option value="surgery">手术室</option>
                        <option value="icu">ICU</option>
                        <option value="isolation">隔离病房</option>
                        <option value="outpatient">门诊大厅</option>
                        <option value="public">公共区域</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>执行人员 *</label>
                    <select name="staff" required>
                        <option value="">请选择人员</option>
                        <option value="li">李阿姨</option>
                        <option value="wang">王师傅</option>
                        <option value="zhang">张师傅</option>
                        <option value="liu">刘阿姨</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>消杀类型 *</label>
                    <select name="type" required>
                        <option value="">请选择类型</option>
                        <option value="routine">日常消杀</option>
                        <option value="deep">深度消杀</option>
                        <option value="emergency">应急消杀</option>
                        <option value="preventive">预防性消杀</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>计划时间 *</label>
                    <input type="datetime-local" name="scheduledTime" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>消毒剂类型 *</label>
                    <select name="disinfectant" required>
                        <option value="">请选择消毒剂</option>
                        <option value="alcohol">75%酒精</option>
                        <option value="chlorine">84消毒液</option>
                        <option value="hydrogen">过氧化氢</option>
                        <option value="quaternary">季铵盐消毒剂</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>消杀方式 *</label>
                    <select name="method" required>
                        <option value="">请选择方式</option>
                        <option value="spray">喷洒消毒</option>
                        <option value="wipe">擦拭消毒</option>
                        <option value="fog">雾化消毒</option>
                        <option value="uv">紫外线照射</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>特殊要求</label>
                <textarea name="requirements" placeholder="请输入特殊要求"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">创建任务</button>
            </div>
        </form>
    `);

    modal.querySelector('.disinfection-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const area = e.target.area.value;
        const staff = e.target.staff.value;
        Utils.showMessage(`消杀任务已创建，区域: ${area}，执行人: ${staff}`, 'success');
        closeModal(modal);
    });
}

// 显示环保产品表单
function showEcoProductForm() {
    const modal = createModal('推荐环保产品', `
        <form class="eco-product-form">
            <div class="form-row">
                <div class="form-group">
                    <label>产品名称 *</label>
                    <input type="text" name="name" required placeholder="请输入产品名称">
                </div>
                <div class="form-group">
                    <label>产品类型 *</label>
                    <select name="type" required>
                        <option value="">请选择类型</option>
                        <option value="cleaner">清洁剂</option>
                        <option value="disinfectant">消毒用品</option>
                        <option value="tool">清洁工具</option>
                        <option value="equipment">清洁设备</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>品牌</label>
                    <input type="text" name="brand" placeholder="请输入品牌">
                </div>
                <div class="form-group">
                    <label>环保认证 *</label>
                    <select name="certification" required>
                        <option value="">请选择认证</option>
                        <option value="leed">LEED认证</option>
                        <option value="green">绿色标章</option>
                        <option value="environment">环境标志</option>
                        <option value="organic">有机认证</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>产品描述 *</label>
                <textarea name="description" required placeholder="请详细描述产品特点和优势"></textarea>
            </div>
            <div class="form-group">
                <label>环保优势 *</label>
                <textarea name="benefits" required placeholder="请说明产品的环保优势"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>碳足迹评级</label>
                    <select name="carbonFootprint">
                        <option value="low">低</option>
                        <option value="medium">中</option>
                        <option value="high">高</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>可降解性</label>
                    <select name="biodegradable">
                        <option value="yes">是</option>
                        <option value="no">否</option>
                        <option value="partial">部分</option>
                    </select>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">推荐产品</button>
            </div>
        </form>
    `);

    modal.querySelector('.eco-product-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const name = e.target.name.value;
        const certification = e.target.certification.value;
        Utils.showMessage(`环保产品"${name}"已推荐，认证类型: ${certification}`, 'success');
        closeModal(modal);
    });
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加基础样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 20px;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    const cancelBtn = modal.querySelector('.cancel-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
