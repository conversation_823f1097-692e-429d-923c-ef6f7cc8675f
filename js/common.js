// 通用JavaScript - 智慧后勤管理系统

document.addEventListener('DOMContentLoaded', function() {
    // 初始化侧边栏
    initSidebar();
    
    // 初始化通知功能
    initNotifications();
    
    // 初始化用户菜单
    initUserMenu();
    
    // 初始化工具提示
    initTooltips();
});

// 侧边栏功能
function initSidebar() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                // 移动端：显示/隐藏侧边栏
                sidebar.classList.toggle('show');
            } else {
                // 桌面端：折叠/展开侧边栏
                sidebar.classList.toggle('collapsed');
                if (mainContent) {
                    mainContent.classList.toggle('expanded');
                }
            }
        });
    }
    
    // 点击侧边栏外部关闭（移动端）
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768) {
            if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
                sidebar.classList.remove('show');
            }
        }
    });
    
    // 窗口大小改变时的处理
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            sidebar.classList.remove('show');
            if (sidebar.classList.contains('collapsed')) {
                mainContent.classList.add('expanded');
            } else {
                mainContent.classList.remove('expanded');
            }
        } else {
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
        }
    });
}

// 通知功能
function initNotifications() {
    const notificationBtn = document.querySelector('.notification-btn');
    
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            showNotificationPanel();
        });
    }
}

function showNotificationPanel() {
    // 创建通知面板
    const panel = document.createElement('div');
    panel.className = 'notification-panel';
    panel.innerHTML = `
        <div class="notification-header">
            <h4>通知中心</h4>
            <button class="close-btn">&times;</button>
        </div>
        <div class="notification-list">
            <div class="notification-item unread">
                <div class="notification-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="notification-content">
                    <h5>采购申请待审批</h5>
                    <p>办公用品采购申请需要您的审批</p>
                    <span class="notification-time">5分钟前</span>
                </div>
            </div>
            <div class="notification-item">
                <div class="notification-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="notification-content">
                    <h5>设备维修完成</h5>
                    <p>会议室投影仪维修已完成</p>
                    <span class="notification-time">1小时前</span>
                </div>
            </div>
            <div class="notification-item">
                <div class="notification-icon">
                    <i class="fas fa-warehouse"></i>
                </div>
                <div class="notification-content">
                    <h5>库存预警</h5>
                    <p>打印纸库存不足，请及时补充</p>
                    <span class="notification-time">3小时前</span>
                </div>
            </div>
        </div>
        <div class="notification-footer">
            <a href="#" class="view-all-btn">查看全部</a>
        </div>
    `;
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .notification-panel {
            position: fixed;
            top: 60px;
            right: 20px;
            width: 350px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            z-index: 1001;
            animation: slideInRight 0.3s ease;
        }
        
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .notification-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .notification-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }
        
        .notification-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .notification-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            gap: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .notification-item:hover {
            background-color: #f8f9fa;
        }
        
        .notification-item.unread {
            background-color: #f0f8ff;
        }
        
        .notification-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-content h5 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .notification-content p {
            margin: 0 0 4px 0;
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }
        
        .notification-time {
            font-size: 12px;
            color: #999;
        }
        
        .notification-footer {
            padding: 15px 20px;
            text-align: center;
            border-top: 1px solid #eee;
        }
        
        .view-all-btn {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(panel);
    
    // 关闭按钮事件
    panel.querySelector('.close-btn').addEventListener('click', function() {
        panel.remove();
        style.remove();
    });
    
    // 点击外部关闭
    setTimeout(() => {
        document.addEventListener('click', function closePanel(e) {
            if (!panel.contains(e.target) && !document.querySelector('.notification-btn').contains(e.target)) {
                panel.remove();
                style.remove();
                document.removeEventListener('click', closePanel);
            }
        });
    }, 100);
}

// 用户菜单功能
function initUserMenu() {
    const userProfile = document.querySelector('.user-profile');
    
    if (userProfile) {
        userProfile.addEventListener('click', function() {
            showUserMenu();
        });
    }
}

function showUserMenu() {
    // 创建用户菜单
    const menu = document.createElement('div');
    menu.className = 'user-menu';
    menu.innerHTML = `
        <div class="user-menu-item">
            <i class="fas fa-user"></i>
            <span>个人资料</span>
        </div>
        <div class="user-menu-item">
            <i class="fas fa-cog"></i>
            <span>系统设置</span>
        </div>
        <div class="user-menu-item">
            <i class="fas fa-question-circle"></i>
            <span>帮助中心</span>
        </div>
        <div class="user-menu-divider"></div>
        <div class="user-menu-item logout">
            <i class="fas fa-sign-out-alt"></i>
            <span>退出登录</span>
        </div>
    `;
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .user-menu {
            position: fixed;
            top: 55px;
            right: 20px;
            width: 200px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 1001;
            animation: slideInDown 0.3s ease;
            overflow: hidden;
        }
        
        @keyframes slideInDown {
            from { transform: translateY(-10px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .user-menu-item {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 14px;
        }
        
        .user-menu-item:hover {
            background-color: #f8f9fa;
        }
        
        .user-menu-item.logout {
            color: #e74c3c;
        }
        
        .user-menu-item.logout:hover {
            background-color: #fdf2f2;
        }
        
        .user-menu-divider {
            height: 1px;
            background-color: #eee;
            margin: 5px 0;
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(menu);
    
    // 点击外部关闭
    setTimeout(() => {
        document.addEventListener('click', function closeMenu(e) {
            if (!menu.contains(e.target) && !document.querySelector('.user-profile').contains(e.target)) {
                menu.remove();
                style.remove();
                document.removeEventListener('click', closeMenu);
            }
        });
    }, 100);
    
    // 菜单项点击事件
    menu.addEventListener('click', function(e) {
        const item = e.target.closest('.user-menu-item');
        if (item) {
            if (item.classList.contains('logout')) {
                if (confirm('确定要退出登录吗？')) {
                    // 这里可以添加退出登录的逻辑
                    window.location.href = 'login.html';
                }
            }
            menu.remove();
            style.remove();
        }
    });
}

// 工具提示功能
function initTooltips() {
    const tooltips = document.querySelectorAll('.tooltip');
    
    tooltips.forEach(tooltip => {
        tooltip.addEventListener('mouseenter', function() {
            const tooltipText = this.querySelector('.tooltiptext');
            if (tooltipText) {
                tooltipText.style.visibility = 'visible';
                tooltipText.style.opacity = '1';
            }
        });
        
        tooltip.addEventListener('mouseleave', function() {
            const tooltipText = this.querySelector('.tooltiptext');
            if (tooltipText) {
                tooltipText.style.visibility = 'hidden';
                tooltipText.style.opacity = '0';
            }
        });
    });
}

// 通用工具函数
const Utils = {
    // 格式化日期
    formatDate: function(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return date.toLocaleDateString('zh-CN');
    },
    
    // 显示消息提示
    showMessage: function(message, type = 'info') {
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        const style = document.createElement('style');
        style.textContent = `
            .message {
                position: fixed;
                top: 80px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                z-index: 1002;
                animation: slideInRight 0.3s ease;
            }
            .message-info { background: #3498db; }
            .message-success { background: #27ae60; }
            .message-warning { background: #f39c12; }
            .message-error { background: #e74c3c; }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(messageEl);
        
        setTimeout(() => {
            messageEl.remove();
            style.remove();
        }, 3000);
    },
    
    // 确认对话框
    confirm: function(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    }
};

// 导出到全局
window.Utils = Utils;
