// 合同管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化菜单切换
    initMenuToggle();

    // 初始化标签页
    initTabs();

    // 初始化图表
    initCharts();

    // 初始化按钮事件
    initButtonEvents();
});

// 初始化菜单切换功能
function initMenuToggle() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');

    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
        });
    }
}

// 初始化标签页
function initTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'overview':
                    initOverviewTab();
                    break;
                case 'clause-review':
                    initClauseReviewTab();
                    break;
                case 'performance-monitoring':
                    initPerformanceTab();
                    break;
                case 'risk-warning':
                    initRiskWarningTab();
                    break;
                case 'e-signature':
                    initESignatureTab();
                    break;
                case 'compliance-check':
                    initComplianceTab();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 合同签署趋势图
    const trendCtx = document.getElementById('contractTrendChart');
    if (trendCtx) {
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '新签合同',
                    data: [45, 52, 38, 61, 48, 55],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '到期合同',
                    data: [28, 35, 42, 31, 39, 33],
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 合同类型分布图
    const typeCtx = document.getElementById('contractTypeChart');
    if (typeCtx) {
        new Chart(typeCtx, {
            type: 'doughnut',
            data: {
                labels: ['采购合同', '服务合同', '租赁合同', '工程合同', '其他'],
                datasets: [{
                    data: [35, 28, 18, 12, 7],
                    backgroundColor: ['#667eea', '#f39c12', '#27ae60', '#e74c3c', '#9b59b6'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建合同
    const newContractBtn = document.getElementById('newContractBtn');
    if (newContractBtn) {
        newContractBtn.addEventListener('click', function() {
            showContractForm();
        });
    }

    // 上传合同
    const uploadContractBtn = document.getElementById('uploadContractBtn');
    if (uploadContractBtn) {
        uploadContractBtn.addEventListener('click', function() {
            triggerFileUpload();
        });
    }

    // 上传文件进行审查
    const uploadForReviewBtn = document.getElementById('uploadForReviewBtn');
    if (uploadForReviewBtn) {
        uploadForReviewBtn.addEventListener('click', function() {
            triggerReviewUpload();
        });
    }

    // 粘贴文本
    const pasteTextBtn = document.getElementById('pasteTextBtn');
    if (pasteTextBtn) {
        pasteTextBtn.addEventListener('click', function() {
            focusTextEditor();
        });
    }

    // 开始审查
    const startReviewBtn = document.getElementById('startReviewBtn');
    if (startReviewBtn) {
        startReviewBtn.addEventListener('click', function() {
            startClauseReview();
        });
    }

    // 生成标准模板
    const generateTemplateBtn = document.getElementById('generateTemplateBtn');
    if (generateTemplateBtn) {
        generateTemplateBtn.addEventListener('click', function() {
            generateStandardTemplate();
        });
    }

    // 发起签署
    const newSignatureBtn = document.getElementById('newSignatureBtn');
    if (newSignatureBtn) {
        newSignatureBtn.addEventListener('click', function() {
            showSignatureForm();
        });
    }

    // 批量签署
    const batchSignBtn = document.getElementById('batchSignBtn');
    if (batchSignBtn) {
        batchSignBtn.addEventListener('click', function() {
            showBatchSignature();
        });
    }

    // 开始合规检测
    const startComplianceCheckBtn = document.getElementById('startComplianceCheckBtn');
    if (startComplianceCheckBtn) {
        startComplianceCheckBtn.addEventListener('click', function() {
            startComplianceCheck();
        });
    }

    // 更新法规库
    const updateRegulationsBtn = document.getElementById('updateRegulationsBtn');
    if (updateRegulationsBtn) {
        updateRegulationsBtn.addEventListener('click', function() {
            updateRegulationsDatabase();
        });
    }
}

// 标签页初始化函数
function initOverviewTab() {
    // 概览标签页初始化
}

function initClauseReviewTab() {
    // 条款审查标签页初始化
}

function initPerformanceTab() {
    // 履约监控标签页初始化
}

function initRiskWarningTab() {
    // 风险预警标签页初始化
}

function initESignatureTab() {
    // 电子签章标签页初始化
}

function initComplianceTab() {
    // 合规检测标签页初始化
}

// 显示合同表单
function showContractForm() {
    const modal = createModal('新建合同', `
        <form class="contract-form">
            <div class="form-row">
                <div class="form-group">
                    <label>合同名称 *</label>
                    <input type="text" name="contractName" required placeholder="请输入合同名称">
                </div>
                <div class="form-group">
                    <label>合同类型 *</label>
                    <select name="contractType" required>
                        <option value="">请选择类型</option>
                        <option value="procurement">采购合同</option>
                        <option value="service">服务合同</option>
                        <option value="lease">租赁合同</option>
                        <option value="engineering">工程合同</option>
                        <option value="other">其他</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>供应商/合作方 *</label>
                    <input type="text" name="supplier" required placeholder="请输入供应商名称">
                </div>
                <div class="form-group">
                    <label>合同金额 *</label>
                    <input type="number" name="amount" required placeholder="请输入合同金额">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>签署日期 *</label>
                    <input type="date" name="signDate" required>
                </div>
                <div class="form-group">
                    <label>到期日期 *</label>
                    <input type="date" name="expireDate" required>
                </div>
            </div>
            <div class="form-group">
                <label>合同描述</label>
                <textarea name="description" placeholder="请输入合同描述"></textarea>
            </div>
            <div class="form-group">
                <label>合同文件</label>
                <div class="upload-area">
                    <input type="file" name="contractFile" accept=".pdf,.doc,.docx" style="display: none;">
                    <div class="upload-placeholder">
                        <i class="fas fa-file-upload"></i>
                        <p>点击上传合同文件</p>
                        <span>支持PDF、Word格式</span>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">创建合同</button>
            </div>
        </form>
    `);

    modal.querySelector('.contract-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const contractName = e.target.contractName.value;
        const contractType = e.target.contractType.value;
        Utils.showMessage(`合同已创建：${contractName} (${contractType})`, 'success');
        closeModal(modal);
    });
}

// 显示签署表单
function showSignatureForm() {
    const modal = createModal('发起合同签署', `
        <form class="signature-form">
            <div class="form-row">
                <div class="form-group">
                    <label>选择合同 *</label>
                    <select name="contractId" required>
                        <option value="">请选择合同</option>
                        <option value="CT-2024-001">医疗设备采购合同</option>
                        <option value="CT-2024-002">保洁服务合同</option>
                        <option value="CT-2024-003">维修服务合同</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>签署类型 *</label>
                    <select name="signatureType" required>
                        <option value="">请选择类型</option>
                        <option value="sequential">顺序签署</option>
                        <option value="parallel">并行签署</option>
                        <option value="single">单人签署</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>签署人员 *</label>
                <div class="signers-list">
                    <div class="signer-item">
                        <select name="signer1" required>
                            <option value="">请选择签署人</option>
                            <option value="zhang">张主任</option>
                            <option value="li">李经理</option>
                            <option value="wang">王院长</option>
                        </select>
                        <select name="role1">
                            <option value="approver">审批人</option>
                            <option value="signer">签署人</option>
                            <option value="witness">见证人</option>
                        </select>
                        <button type="button" class="btn btn-sm btn-secondary add-signer">+</button>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>签署说明</label>
                <textarea name="signatureNote" placeholder="请输入签署说明"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">发起签署</button>
            </div>
        </form>
    `);

    modal.querySelector('.signature-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const contractId = e.target.contractId.value;
        Utils.showMessage(`签署流程已发起：${contractId}`, 'success');
        closeModal(modal);
    });
}

// 触发文件上传
function triggerFileUpload() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.doc,.docx';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            Utils.showMessage(`文件已上传：${file.name}`, 'success');
        }
    };
    input.click();
}

// 触发审查文件上传
function triggerReviewUpload() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.doc,.docx';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            Utils.showMessage('正在解析合同文件...', 'info');
            setTimeout(() => {
                Utils.showMessage('文件解析完成，请点击开始审查', 'success');
            }, 2000);
        }
    };
    input.click();
}

// 聚焦文本编辑器
function focusTextEditor() {
    const textarea = document.getElementById('contractText');
    if (textarea) {
        textarea.focus();
        Utils.showMessage('请粘贴合同文本内容', 'info');
    }
}

// 开始条款审查
function startClauseReview() {
    const textarea = document.getElementById('contractText');
    if (textarea && textarea.value.trim()) {
        Utils.showMessage('正在进行AI智能条款审查...', 'info');
        setTimeout(() => {
            Utils.showMessage('条款审查完成，发现2个高风险项和5个中风险项', 'warning');
        }, 3000);
    } else {
        Utils.showMessage('请先输入或上传合同内容', 'warning');
    }
}

// 生成标准模板
function generateStandardTemplate() {
    Utils.showMessage('正在生成标准合同模板...', 'info');
    setTimeout(() => {
        Utils.showMessage('标准模板已生成，可下载使用', 'success');
    }, 2000);
}

// 显示批量签署
function showBatchSignature() {
    Utils.showMessage('批量签署功能开发中...', 'info');
}

// 开始合规检测
function startComplianceCheck() {
    Utils.showMessage('正在进行法律合规检测...', 'info');
    setTimeout(() => {
        Utils.showMessage('合规检测完成，发现2个违规风险点', 'warning');
    }, 4000);
}

// 更新法规库
function updateRegulationsDatabase() {
    Utils.showMessage('正在更新法规数据库...', 'info');
    setTimeout(() => {
        Utils.showMessage('法规库更新完成，新增8项法规变更', 'success');
    }, 3000);
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-container">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 关闭模态框事件
    modal.querySelector('.modal-close').addEventListener('click', () => closeModal(modal));
    const cancelBtn = modal.querySelector('.cancel-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => closeModal(modal));
    }
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal(modal);
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
