// 首页JavaScript - 智慧后勤管理系统

document.addEventListener('DOMContentLoaded', function() {
    // 初始化图表
    initCharts();
    
    // 初始化快捷服务
    initQuickServices();
    
    // 初始化智能助手
    initAIAssistant();
    
    // 初始化数据刷新
    initDataRefresh();
    
    // 添加动画效果
    initAnimations();
});

// 初始化图表
function initCharts() {
    // 月度支出统计图表
    const expenseCtx = document.getElementById('expenseChart');
    if (expenseCtx) {
        new Chart(expenseCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '支出金额(万元)',
                    data: [120, 150, 180, 140, 200, 160],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                }
            }
        });
    }
    
    // 设备状态分布图表
    const equipmentCtx = document.getElementById('equipmentChart');
    if (equipmentCtx) {
        new Chart(equipmentCtx, {
            type: 'doughnut',
            data: {
                labels: ['正常运行', '维修中', '待修', '停用'],
                datasets: [{
                    data: [65, 15, 12, 8],
                    backgroundColor: [
                        '#27ae60',
                        '#f39c12',
                        '#e74c3c',
                        '#95a5a6'
                    ],
                    borderWidth: 0,
                    cutout: '60%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
    }
}

// 初始化快捷服务
function initQuickServices() {
    const serviceItems = document.querySelectorAll('.service-item');
    
    serviceItems.forEach(item => {
        item.addEventListener('click', function() {
            const title = this.querySelector('h4').textContent;
            
            // 添加点击动画
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // 根据服务类型跳转或执行操作
            switch(title) {
                case '采购申请':
                    showQuickForm('采购申请', [
                        { label: '申请部门', type: 'text', required: true },
                        { label: '物品名称', type: 'text', required: true },
                        { label: '数量', type: 'number', required: true },
                        { label: '预算金额', type: 'number', required: true },
                        { label: '用途说明', type: 'textarea', required: true }
                    ]);
                    break;
                case '设备报修':
                    showQuickForm('设备报修', [
                        { label: '设备名称', type: 'text', required: true },
                        { label: '设备位置', type: 'text', required: true },
                        { label: '故障描述', type: 'textarea', required: true },
                        { label: '紧急程度', type: 'select', options: ['一般', '紧急', '非常紧急'], required: true }
                    ]);
                    break;
                case '车辆申请':
                    showQuickForm('车辆申请', [
                        { label: '用车日期', type: 'date', required: true },
                        { label: '用车时间', type: 'time', required: true },
                        { label: '目的地', type: 'text', required: true },
                        { label: '用车事由', type: 'textarea', required: true }
                    ]);
                    break;
                default:
                    Utils.showMessage(`${title}功能正在开发中...`, 'info');
            }
        });
    });
}

// 显示快捷表单
function showQuickForm(title, fields) {
    const modal = document.createElement('div');
    modal.className = 'quick-form-modal';
    
    let fieldsHtml = '';
    fields.forEach(field => {
        let inputHtml = '';
        switch(field.type) {
            case 'textarea':
                inputHtml = `<textarea name="${field.label}" ${field.required ? 'required' : ''} placeholder="请输入${field.label}"></textarea>`;
                break;
            case 'select':
                const options = field.options.map(opt => `<option value="${opt}">${opt}</option>`).join('');
                inputHtml = `<select name="${field.label}" ${field.required ? 'required' : ''}><option value="">请选择${field.label}</option>${options}</select>`;
                break;
            default:
                inputHtml = `<input type="${field.type}" name="${field.label}" ${field.required ? 'required' : ''} placeholder="请输入${field.label}">`;
        }
        
        fieldsHtml += `
            <div class="form-group">
                <label>${field.label}${field.required ? ' *' : ''}</label>
                ${inputHtml}
            </div>
        `;
    });
    
    modal.innerHTML = `
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <form class="quick-form">
                ${fieldsHtml}
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                    <button type="submit" class="btn btn-primary">提交申请</button>
                </div>
            </form>
        </div>
    `;
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .quick-form-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1003;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }
        
        @keyframes modalSlideIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .quick-form {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(modal);
    
    // 事件处理
    const closeModal = () => {
        modal.remove();
        style.remove();
    };
    
    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    modal.querySelector('.cancel-btn').addEventListener('click', closeModal);
    modal.querySelector('.modal-overlay').addEventListener('click', closeModal);
    
    modal.querySelector('.quick-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('申请已提交，请等待审批', 'success');
        closeModal();
    });
}

// 初始化智能助手
function initAIAssistant() {
    const suggestionBtns = document.querySelectorAll('.suggestion-btn');
    const assistantText = document.querySelector('.assistant-text');
    const assistantSend = document.querySelector('.assistant-send');
    
    // 建议按钮点击
    suggestionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const question = this.textContent;
            assistantText.value = question;
            sendMessage(question);
        });
    });
    
    // 发送按钮点击
    if (assistantSend) {
        assistantSend.addEventListener('click', function() {
            const message = assistantText.value.trim();
            if (message) {
                sendMessage(message);
                assistantText.value = '';
            }
        });
    }
    
    // 回车发送
    if (assistantText) {
        assistantText.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const message = this.value.trim();
                if (message) {
                    sendMessage(message);
                    this.value = '';
                }
            }
        });
    }
}

// 发送消息给智能助手
function sendMessage(message) {
    // 模拟AI回复
    const responses = {
        '如何申请采购？': '您可以点击快捷服务中的"采购申请"按钮，填写相关信息后提交。系统会自动流转到相关审批人员。',
        '设备报修流程': '设备报修流程：1. 点击"设备报修"按钮 2. 填写设备信息和故障描述 3. 提交申请 4. 等待维修人员联系 5. 维修完成确认',
        '查看库存状态': '您可以在"仓库管理"模块中查看实时库存状态，包括库存数量、预警信息等。'
    };
    
    const response = responses[message] || '感谢您的提问，我会尽快为您提供帮助。如需更多信息，请联系系统管理员。';
    
    // 显示回复
    setTimeout(() => {
        Utils.showMessage(response, 'info');
    }, 1000);
}

// 初始化数据刷新
function initDataRefresh() {
    // 每30秒刷新一次数据
    setInterval(updateDashboardData, 30000);
}

// 更新仪表板数据
function updateDashboardData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        const trend = card.querySelector('.card-trend span');
        
        if (number && !isNaN(parseInt(number.textContent))) {
            // 随机变化数据
            const currentValue = parseInt(number.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
            const newValue = Math.max(0, currentValue + change);
            
            if (newValue !== currentValue) {
                number.textContent = newValue;
                
                // 更新趋势
                if (change > 0) {
                    trend.textContent = '+' + Math.random().toFixed(1) + '%';
                    trend.parentElement.className = 'card-trend up';
                } else if (change < 0) {
                    trend.textContent = '-' + Math.random().toFixed(1) + '%';
                    trend.parentElement.className = 'card-trend down';
                }
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 初始化动画效果
function initAnimations() {
    // 滚动动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate__animated', 'animate__fadeInUp');
            }
        });
    }, observerOptions);
    
    // 观察所有卡片元素
    document.querySelectorAll('.service-item, .chart-card, .activity-item').forEach(el => {
        observer.observe(el);
    });
}
