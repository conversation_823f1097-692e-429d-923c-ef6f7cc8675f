// 宿舍管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化图表
    initCharts();

    // 初始化满意度调查图表
    initSurveyCharts();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化筛选功能
    initFilters();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'access':
                    initAccessTab();
                    break;
                case 'allocation':
                    initAllocationTab();
                    break;
                case 'billing':
                    initBillingTab();
                    break;
                case 'facilities':
                    initFacilitiesTab();
                    break;
                case 'survey':
                    initSurveyTab();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 表计用量图表
    const meterChart1 = document.getElementById('meterChart1');
    if (meterChart1) {
        new Chart(meterChart1, {
            type: 'line',
            data: {
                labels: ['1日', '5日', '10日', '15日', '20日', '今日'],
                datasets: [{
                    label: '水表读数',
                    data: [1210, 1215, 1222, 1228, 1232, 1234.5],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    const meterChart2 = document.getElementById('meterChart2');
    if (meterChart2) {
        new Chart(meterChart2, {
            type: 'line',
            data: {
                labels: ['1日', '5日', '10日', '15日', '20日', '今日'],
                datasets: [{
                    label: '电表读数',
                    data: [2450, 2478, 2510, 2535, 2555, 2567.8],
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
}

// 初始化满意度调查图表
function initSurveyCharts() {
    // 满意度趋势图
    const satisfactionTrendCtx = document.getElementById('satisfactionTrendChart');
    if (satisfactionTrendCtx) {
        new Chart(satisfactionTrendCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '总体满意度',
                    data: [4.2, 4.3, 4.1, 4.4, 4.5, 4.6],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '设施满意度',
                    data: [4.0, 4.1, 3.9, 4.2, 4.3, 4.4],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '服务满意度',
                    data: [4.3, 4.4, 4.2, 4.5, 4.6, 4.7],
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 3.5,
                        max: 5,
                        grid: {
                            color: '#f0f0f0'
                        },
                        title: {
                            display: true,
                            text: '满意度评分'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 问题分类统计图
    const issuesCategoryCtx = document.getElementById('issuesCategoryChart');
    if (issuesCategoryCtx) {
        new Chart(issuesCategoryCtx, {
            type: 'doughnut',
            data: {
                labels: ['设施设备', '住宿环境', '服务质量', '安全管理', '其他'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: ['#e74c3c', '#f39c12', '#3498db', '#27ae60', '#9b59b6'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 新增住户
    const newResidentBtn = document.getElementById('newResidentBtn');
    if (newResidentBtn) {
        newResidentBtn.addEventListener('click', function() {
            showResidentForm();
        });
    }

    // 房间分配
    const roomAllocationBtn = document.getElementById('roomAllocationBtn');
    if (roomAllocationBtn) {
        roomAllocationBtn.addEventListener('click', function() {
            showRoomAllocation();
        });
    }

    // 宿舍报告
    const dormitoryReportBtn = document.getElementById('dormitoryReportBtn');
    if (dormitoryReportBtn) {
        dormitoryReportBtn.addEventListener('click', function() {
            generateDormitoryReport();
        });
    }

    // 添加验证方式
    const addAccessMethodBtn = document.getElementById('addAccessMethodBtn');
    if (addAccessMethodBtn) {
        addAccessMethodBtn.addEventListener('click', function() {
            showAccessMethodForm();
        });
    }

    // 新增访客
    const newVisitorBtn = document.getElementById('newVisitorBtn');
    if (newVisitorBtn) {
        newVisitorBtn.addEventListener('click', function() {
            showVisitorForm();
        });
    }

    // 导出记录
    const exportLogsBtn = document.getElementById('exportLogsBtn');
    if (exportLogsBtn) {
        exportLogsBtn.addEventListener('click', function() {
            exportAccessLogs();
        });
    }

    // 智能分配
    const autoAllocateBtn = document.getElementById('autoAllocateBtn');
    if (autoAllocateBtn) {
        autoAllocateBtn.addEventListener('click', function() {
            runAutoAllocation();
        });
    }

    // 新增换宿申请
    const newTransferBtn = document.getElementById('newTransferBtn');
    if (newTransferBtn) {
        newTransferBtn.addEventListener('click', function() {
            showTransferForm();
        });
    }

    // 同步读数
    const syncMeterBtn = document.getElementById('syncMeterBtn');
    if (syncMeterBtn) {
        syncMeterBtn.addEventListener('click', function() {
            syncMeterReadings();
        });
    }

    // 新建预约
    const newBookingBtn = document.getElementById('newBookingBtn');
    if (newBookingBtn) {
        newBookingBtn.addEventListener('click', function() {
            showBookingForm();
        });
    }

    // 发起调查
    const newSurveyBtn = document.getElementById('newSurveyBtn');
    if (newSurveyBtn) {
        newSurveyBtn.addEventListener('click', function() {
            showSurveyForm();
        });
    }

    // 访客操作按钮
    const visitorActionBtns = document.querySelectorAll('.visitor-actions .btn');
    visitorActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const visitorItem = this.closest('.visitor-item');
            const visitorName = visitorItem.querySelector('h4').textContent;
            handleVisitorAction(action, visitorName);
        });
    });

    // 房间操作按钮
    const roomActionBtns = document.querySelectorAll('.room-actions .btn');
    roomActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const roomCard = this.closest('.room-card');
            const roomNumber = roomCard.querySelector('.room-number').textContent;
            handleRoomAction(action, roomNumber);
        });
    });

    // 换宿申请操作按钮
    const requestActionBtns = document.querySelectorAll('.request-actions .btn');
    requestActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const requestItem = this.closest('.request-item');
            const requestTitle = requestItem.querySelector('h4').textContent;
            handleRequestAction(action, requestTitle);
        });
    });
}

// 显示住户表单
function showResidentForm() {
    const modal = createModal('新增住户', `
        <form class="resident-form">
            <div class="form-row">
                <div class="form-group">
                    <label>姓名 *</label>
                    <input type="text" name="name" required placeholder="请输入住户姓名">
                </div>
                <div class="form-group">
                    <label>性别 *</label>
                    <select name="gender" required>
                        <option value="">请选择性别</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>联系电话 *</label>
                    <input type="tel" name="phone" required placeholder="请输入联系电话">
                </div>
                <div class="form-group">
                    <label>部门 *</label>
                    <select name="department" required>
                        <option value="">请选择部门</option>
                        <option value="medical">医护部</option>
                        <option value="admin">行政部</option>
                        <option value="logistics">后勤部</option>
                        <option value="finance">财务部</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>工作班次</label>
                    <select name="shift">
                        <option value="">请选择班次</option>
                        <option value="day">白班</option>
                        <option value="night">夜班</option>
                        <option value="rotation">轮班</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>房间偏好</label>
                    <select name="roomPreference">
                        <option value="">无特殊要求</option>
                        <option value="single">单人间</option>
                        <option value="double">双人间</option>
                        <option value="quiet">安静楼层</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>身份证号 *</label>
                <input type="text" name="idCard" required placeholder="请输入身份证号">
            </div>
            <div class="form-group">
                <label>备注</label>
                <textarea name="notes" placeholder="请输入备注信息"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    `);

    modal.querySelector('.resident-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const name = e.target.name.value;
        Utils.showMessage(`住户"${name}"信息已保存`, 'success');
        closeModal(modal);
    });
}

// 显示房间分配
function showRoomAllocation() {
    Utils.showMessage('正在打开房间分配页面...', 'info');
}

// 生成宿舍报告
function generateDormitoryReport() {
    Utils.showMessage('正在生成宿舍管理报告...', 'info');
    setTimeout(() => {
        Utils.showMessage('宿舍管理报告生成成功', 'success');
    }, 2000);
}

// 显示验证方式表单
function showAccessMethodForm() {
    Utils.showMessage('正在配置新的身份验证方式...', 'info');
}

// 显示访客表单
function showVisitorForm() {
    const modal = createModal('新增访客申请', `
        <form class="visitor-form">
            <div class="form-row">
                <div class="form-group">
                    <label>访客姓名 *</label>
                    <input type="text" name="visitorName" required placeholder="请输入访客姓名">
                </div>
                <div class="form-group">
                    <label>联系电话 *</label>
                    <input type="tel" name="visitorPhone" required placeholder="请输入访客电话">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>申请人 *</label>
                    <input type="text" name="applicant" required placeholder="请输入申请人姓名">
                </div>
                <div class="form-group">
                    <label>房间号 *</label>
                    <input type="text" name="roomNumber" required placeholder="如: A301">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>访问开始时间 *</label>
                    <input type="datetime-local" name="startTime" required>
                </div>
                <div class="form-group">
                    <label>访问结束时间 *</label>
                    <input type="datetime-local" name="endTime" required>
                </div>
            </div>
            <div class="form-group">
                <label>访问事由 *</label>
                <textarea name="purpose" required placeholder="请详细说明访问事由"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交申请</button>
            </div>
        </form>
    `);

    modal.querySelector('.visitor-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const visitorName = e.target.visitorName.value;
        Utils.showMessage(`访客申请已提交，访客: ${visitorName}`, 'success');
        closeModal(modal);
    });
}

// 导出出入记录
function exportAccessLogs() {
    Utils.showMessage('正在导出出入记录...', 'info');
    setTimeout(() => {
        Utils.showMessage('出入记录导出成功', 'success');
    }, 1500);
}

// 运行智能分配
function runAutoAllocation() {
    Utils.showMessage('正在运行智能分配算法...', 'info');
    setTimeout(() => {
        Utils.showMessage('智能分配完成，已优化房间配置', 'success');
        // 更新房间状态显示
        updateRoomStatus();
    }, 3000);
}

// 更新房间状态
function updateRoomStatus() {
    const roomCards = document.querySelectorAll('.room-card');
    roomCards.forEach(card => {
        // 模拟状态更新
        const statusElement = card.querySelector('.room-status');
        if (statusElement && Math.random() > 0.7) {
            // 随机更新一些房间状态
            statusElement.textContent = '推荐';
            statusElement.className = 'room-status recommended';
        }
    });
}

// 显示换宿申请表单
function showTransferForm() {
    Utils.showMessage('正在打开换宿申请表单...', 'info');
}

// 同步表计读数
function syncMeterReadings() {
    Utils.showMessage('正在同步智能表计读数...', 'info');
    setTimeout(() => {
        Utils.showMessage('表计读数同步完成', 'success');
        // 更新读数显示
        updateMeterReadings();
    }, 2000);
}

// 更新表计读数
function updateMeterReadings() {
    const meterCards = document.querySelectorAll('.meter-card');
    meterCards.forEach(card => {
        const currentReading = card.querySelector('.current-reading .value');
        if (currentReading) {
            // 模拟读数更新
            const currentValue = parseFloat(currentReading.textContent);
            const newValue = (currentValue + Math.random() * 5).toFixed(1);
            currentReading.textContent = newValue + (currentReading.textContent.includes('m³') ? ' m³' : ' kWh');
        }
    });
}

// 处理访客操作
function handleVisitorAction(action, visitorName) {
    switch(action) {
        case '通过':
            Utils.showMessage(`已通过${visitorName}的访客申请`, 'success');
            break;
        case '拒绝':
            Utils.showMessage(`已拒绝${visitorName}的访客申请`, 'info');
            break;
        case '详情':
        case '查看详情':
            Utils.showMessage(`正在查看${visitorName}的详细信息...`, 'info');
            break;
        case '生成通行码':
            Utils.showMessage(`正在为${visitorName}生成临时通行码...`, 'info');
            break;
    }
}

// 处理房间操作
function handleRoomAction(action, roomNumber) {
    switch(action) {
        case '分配住户':
            Utils.showMessage(`正在为房间${roomNumber}分配住户...`, 'info');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看房间${roomNumber}详细信息...`, 'info');
            break;
        case '推荐入住':
            Utils.showMessage(`正在为房间${roomNumber}推荐合适的住户...`, 'info');
            break;
    }
}

// 处理换宿申请操作
function handleRequestAction(action, requestTitle) {
    switch(action) {
        case '同意':
            Utils.showMessage(`已同意"${requestTitle}"`, 'success');
            break;
        case '拒绝':
            Utils.showMessage(`已拒绝"${requestTitle}"`, 'info');
            break;
        case '详情':
            Utils.showMessage(`正在查看"${requestTitle}"详细信息...`, 'info');
            break;
        case '安排搬迁':
            Utils.showMessage(`正在安排"${requestTitle}"的搬迁事宜...`, 'info');
            break;
    }
}

// 初始化筛选功能
function initFilters() {
    // 访客筛选
    const visitorFilters = document.querySelectorAll('.visitor-filter');
    visitorFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterVisitors(filterValue);
        });
    });

    // 楼栋筛选
    const buildingFilters = document.querySelectorAll('.building-filter');
    buildingFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByBuilding(filterValue);
        });
    });

    // 房型筛选
    const roomTypeFilters = document.querySelectorAll('.room-type-filter');
    roomTypeFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByRoomType(filterValue);
        });
    });

    // 表计类型筛选
    const meterTypeFilters = document.querySelectorAll('.meter-type-filter');
    meterTypeFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByMeterType(filterValue);
        });
    });
}

// 筛选访客
function filterVisitors(filterValue) {
    const visitorItems = document.querySelectorAll('.visitor-item');
    
    visitorItems.forEach(item => {
        if (filterValue === '全部访客') {
            item.style.display = 'flex';
        } else if (filterValue === '待审核') {
            if (item.classList.contains('pending')) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        } else if (filterValue === '已通过') {
            if (item.classList.contains('approved')) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 按楼栋筛选
function filterByBuilding(filterValue) {
    const buildingSections = document.querySelectorAll('.building-section');
    
    buildingSections.forEach(section => {
        if (filterValue === '全部楼栋') {
            section.style.display = 'block';
        } else {
            const buildingName = section.querySelector('h4').textContent;
            if (buildingName.includes(filterValue)) {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        }
    });
}

// 按房型筛选
function filterByRoomType(filterValue) {
    const roomCards = document.querySelectorAll('.room-card');
    
    roomCards.forEach(card => {
        if (filterValue === '全部房型') {
            card.style.display = 'block';
        } else {
            const roomType = card.querySelector('.room-type').textContent;
            if (roomType.includes(filterValue)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// 按表计类型筛选
function filterByMeterType(filterValue) {
    const meterCards = document.querySelectorAll('.meter-card');
    
    meterCards.forEach(card => {
        if (filterValue === '全部表计') {
            card.style.display = 'block';
        } else {
            const meterTitle = card.querySelector('h4').textContent;
            if (meterTitle.includes(filterValue)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateDormitoryData, 30000);
}

// 更新宿舍数据
function updateDormitoryData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number && !isNaN(parseInt(number.textContent))) {
            // 随机变化数据
            const currentValue = parseInt(number.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
            const newValue = Math.max(0, currentValue + change);
            
            if (newValue !== currentValue) {
                number.textContent = newValue;
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 标签页初始化函数
function initAccessTab() {
    // 门禁系统标签页初始化
}

function initAllocationTab() {
    // 资源分配标签页初始化
}

function initBillingTab() {
    // 费用分摊标签页初始化
    // 重新初始化图表以确保正确显示
    setTimeout(() => {
        initCharts();
    }, 100);
}

function initFacilitiesTab() {
    // 设施预约标签页初始化
}

function initSurveyTab() {
    // 满意度调查标签页初始化
    // 重新初始化图表以确保正确显示
    setTimeout(() => {
        initSurveyCharts();
    }, 100);
}

// 显示预约表单
function showBookingForm() {
    const modal = createModal('新建设施预约', `
        <form class="booking-form">
            <div class="form-row">
                <div class="form-group">
                    <label>设施类型 *</label>
                    <select name="facilityType" required>
                        <option value="">请选择设施</option>
                        <option value="gym">健身房</option>
                        <option value="laundry">洗衣房</option>
                        <option value="study">自习室</option>
                        <option value="entertainment">娱乐室</option>
                        <option value="meeting">会议室</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>预约日期 *</label>
                    <input type="date" name="bookingDate" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>开始时间 *</label>
                    <input type="time" name="startTime" required>
                </div>
                <div class="form-group">
                    <label>结束时间 *</label>
                    <input type="time" name="endTime" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>预约人数</label>
                    <input type="number" name="peopleCount" min="1" max="20" placeholder="请输入人数">
                </div>
                <div class="form-group">
                    <label>联系电话 *</label>
                    <input type="tel" name="phone" required placeholder="请输入联系电话">
                </div>
            </div>
            <div class="form-group">
                <label>预约用途</label>
                <textarea name="purpose" placeholder="请说明预约用途"></textarea>
            </div>
            <div class="form-group">
                <label>特殊要求</label>
                <textarea name="requirements" placeholder="请输入特殊要求"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交预约</button>
            </div>
        </form>
    `);

    modal.querySelector('.booking-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const facilityType = e.target.facilityType.value;
        const bookingDate = e.target.bookingDate.value;
        Utils.showMessage(`${facilityType}预约已提交，预约日期: ${bookingDate}`, 'success');
        closeModal(modal);
    });
}

// 显示调查表单
function showSurveyForm() {
    const modal = createModal('发起满意度调查', `
        <form class="survey-form">
            <div class="form-row">
                <div class="form-group">
                    <label>调查标题 *</label>
                    <input type="text" name="surveyTitle" required placeholder="请输入调查标题">
                </div>
                <div class="form-group">
                    <label>调查类型 *</label>
                    <select name="surveyType" required>
                        <option value="">请选择类型</option>
                        <option value="satisfaction">满意度调查</option>
                        <option value="facility">设施评价</option>
                        <option value="service">服务质量</option>
                        <option value="environment">环境评估</option>
                        <option value="suggestion">意见建议</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>目标人群 *</label>
                    <select name="targetGroup" required>
                        <option value="">请选择人群</option>
                        <option value="all">全体住户</option>
                        <option value="building_a">A栋住户</option>
                        <option value="building_b">B栋住户</option>
                        <option value="building_c">C栋住户</option>
                        <option value="medical">医护人员</option>
                        <option value="admin">行政人员</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>调查期限 *</label>
                    <input type="date" name="deadline" required>
                </div>
            </div>
            <div class="form-group">
                <label>调查说明 *</label>
                <textarea name="description" required placeholder="请详细说明调查目的和要求"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">发起调查</button>
            </div>
        </form>
    `);

    modal.querySelector('.survey-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const surveyTitle = e.target.surveyTitle.value;
        const targetGroup = e.target.targetGroup.value;
        Utils.showMessage(`满意度调查"${surveyTitle}"已发起，目标人群: ${targetGroup}`, 'success');
        closeModal(modal);
    });
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加基础样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 20px;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    const cancelBtn = modal.querySelector('.cancel-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
