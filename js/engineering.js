// 工程管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化搜索功能
    initSearch();
    
    // 初始化文件树
    initFileTree();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'monitoring':
                    initMonitoringTab();
                    break;
                case 'quality':
                    initQualityTab();
                    break;
                case 'hidden':
                    initHiddenWorksTab();
                    break;
                case 'archives':
                    initArchivesTab();
                    break;
                case 'risk':
                    initRiskTab();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 甘特图
    const ganttCtx = document.getElementById('ganttChart');
    if (ganttCtx) {
        new Chart(ganttCtx, {
            type: 'bar',
            data: {
                labels: ['设计阶段', '采购阶段', '施工阶段', '验收阶段'],
                datasets: [{
                    label: '办公楼装修项目',
                    data: [15, 10, 45, 5],
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: '#667eea',
                    borderWidth: 1
                }, {
                    label: '停车场扩建项目',
                    data: [20, 15, 60, 10],
                    backgroundColor: 'rgba(243, 156, 18, 0.8)',
                    borderColor: '#f39c12',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        },
                        title: {
                            display: true,
                            text: '天数'
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建项目
    const newProjectBtn = document.getElementById('newProjectBtn');
    if (newProjectBtn) {
        newProjectBtn.addEventListener('click', function() {
            showProjectForm();
        });
    }

    // 质量报告
    const qualityReportBtn = document.getElementById('qualityReportBtn');
    if (qualityReportBtn) {
        qualityReportBtn.addEventListener('click', function() {
            generateQualityReport();
        });
    }

    // 风险分析
    const riskAnalysisBtn = document.getElementById('riskAnalysisBtn');
    if (riskAnalysisBtn) {
        riskAnalysisBtn.addEventListener('click', function() {
            showRiskAnalysis();
        });
    }

    // 新建评估
    const newAssessmentBtn = document.getElementById('newAssessmentBtn');
    if (newAssessmentBtn) {
        newAssessmentBtn.addEventListener('click', function() {
            showAssessmentForm();
        });
    }

    // 新增隐蔽工程
    const newHiddenWorkBtn = document.getElementById('newHiddenWorkBtn');
    if (newHiddenWorkBtn) {
        newHiddenWorkBtn.addEventListener('click', function() {
            showHiddenWorkForm();
        });
    }

    // 扫描查询
    const scanQRBtn = document.getElementById('scanQRBtn');
    if (scanQRBtn) {
        scanQRBtn.addEventListener('click', function() {
            showQRScanner();
        });
    }

    // 项目操作按钮
    const projectActionBtns = document.querySelectorAll('.project-actions .btn');
    projectActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const projectCard = this.closest('.project-card');
            const projectName = projectCard.querySelector('h4').textContent;
            handleProjectAction(action, projectName);
        });
    });

    // 风险处理按钮
    const riskActionBtns = document.querySelectorAll('.alert-actions .btn');
    riskActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const alertItem = this.closest('.alert-item');
            const riskTitle = alertItem.querySelector('h4').textContent;
            handleRiskAction(action, riskTitle);
        });
    });
}

// 显示项目表单
function showProjectForm() {
    const modal = createModal('新建工程项目', `
        <form class="project-form">
            <div class="form-row">
                <div class="form-group">
                    <label>项目名称 *</label>
                    <input type="text" name="projectName" required placeholder="请输入项目名称">
                </div>
                <div class="form-group">
                    <label>项目类型 *</label>
                    <select name="projectType" required>
                        <option value="">请选择项目类型</option>
                        <option value="renovation">装修改造</option>
                        <option value="construction">新建工程</option>
                        <option value="maintenance">维修工程</option>
                        <option value="infrastructure">基础设施</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>项目经理 *</label>
                    <input type="text" name="projectManager" required placeholder="请输入项目经理">
                </div>
                <div class="form-group">
                    <label>施工单位</label>
                    <input type="text" name="contractor" placeholder="请输入施工单位">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>计划开始时间 *</label>
                    <input type="date" name="startDate" required>
                </div>
                <div class="form-group">
                    <label>计划完成时间 *</label>
                    <input type="date" name="endDate" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>项目预算 *</label>
                    <input type="number" name="budget" required placeholder="请输入项目预算(万元)">
                </div>
                <div class="form-group">
                    <label>优先级</label>
                    <select name="priority">
                        <option value="normal">一般</option>
                        <option value="high">高</option>
                        <option value="urgent">紧急</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>项目描述 *</label>
                <textarea name="description" required placeholder="请详细描述项目内容和要求"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">创建项目</button>
            </div>
        </form>
    `);

    modal.querySelector('.project-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('项目创建成功，已加入项目管理列表', 'success');
        closeModal(modal);
    });
}

// 显示评估表单
function showAssessmentForm() {
    const modal = createModal('新建质量评估', `
        <form class="assessment-form">
            <div class="form-group">
                <label>评估项目 *</label>
                <select name="project" required>
                    <option value="">请选择项目</option>
                    <option value="office">办公楼装修改造项目</option>
                    <option value="parking">停车场扩建工程</option>
                </select>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>评估阶段 *</label>
                    <select name="phase" required>
                        <option value="">请选择评估阶段</option>
                        <option value="design">设计阶段</option>
                        <option value="construction">施工阶段</option>
                        <option value="completion">竣工阶段</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>评估人员 *</label>
                    <input type="text" name="assessor" required placeholder="请输入评估人员">
                </div>
            </div>
            <div class="form-group">
                <label>评估内容 *</label>
                <textarea name="content" required placeholder="请详细描述评估内容和检查项目"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>材料合格率 (%)</label>
                    <input type="number" name="materialRate" min="0" max="100" placeholder="0-100">
                </div>
                <div class="form-group">
                    <label>施工标准达标率 (%)</label>
                    <input type="number" name="standardRate" min="0" max="100" placeholder="0-100">
                </div>
            </div>
            <div class="form-group">
                <label>评估结论</label>
                <textarea name="conclusion" placeholder="请输入评估结论和建议"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交评估</button>
            </div>
        </form>
    `);

    modal.querySelector('.assessment-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('质量评估已提交', 'success');
        closeModal(modal);
    });
}

// 显示隐蔽工程表单
function showHiddenWorkForm() {
    const modal = createModal('新增隐蔽工程', `
        <form class="hidden-work-form">
            <div class="form-row">
                <div class="form-group">
                    <label>工程类型 *</label>
                    <select name="workType" required>
                        <option value="">请选择工程类型</option>
                        <option value="electrical">电气工程</option>
                        <option value="plumbing">给排水工程</option>
                        <option value="network">网络布线</option>
                        <option value="hvac">暖通工程</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>所属项目 *</label>
                    <select name="project" required>
                        <option value="">请选择项目</option>
                        <option value="office">办公楼装修改造项目</option>
                        <option value="parking">停车场扩建工程</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>工程名称 *</label>
                <input type="text" name="workName" required placeholder="请输入工程名称">
            </div>
            <div class="form-group">
                <label>工程位置 *</label>
                <input type="text" name="location" required placeholder="请输入工程位置">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>施工单位 *</label>
                    <input type="text" name="contractor" required placeholder="请输入施工单位">
                </div>
                <div class="form-group">
                    <label>施工日期 *</label>
                    <input type="date" name="constructionDate" required>
                </div>
            </div>
            <div class="form-group">
                <label>材料规格 *</label>
                <input type="text" name="materials" required placeholder="请输入材料规格和型号">
            </div>
            <div class="form-group">
                <label>工程描述</label>
                <textarea name="description" placeholder="请详细描述工程内容和技术要求"></textarea>
            </div>
            <div class="form-group">
                <label>相关图纸</label>
                <input type="file" name="drawings" multiple accept=".pdf,.dwg,.jpg,.png">
                <small>支持PDF、DWG、JPG、PNG格式，可多选</small>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交记录</button>
            </div>
        </form>
    `);

    modal.querySelector('.hidden-work-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const workName = e.target.workName.value;
        const qrCode = 'HW-' + new Date().getFullYear() + '-' + String(Math.floor(Math.random() * 1000)).padStart(3, '0');
        Utils.showMessage(`隐蔽工程"${workName}"已记录，二维码: ${qrCode}`, 'success');
        closeModal(modal);
    });
}

// 显示二维码扫描器
function showQRScanner() {
    const modal = createModal('扫描二维码查询', `
        <div class="qr-scanner">
            <div class="scanner-area">
                <div class="scanner-frame">
                    <i class="fas fa-qrcode"></i>
                    <p>请将二维码对准扫描框</p>
                </div>
            </div>
            <div class="manual-input">
                <h4>或手动输入编号</h4>
                <div class="input-group">
                    <input type="text" placeholder="请输入隐蔽工程编号" class="qr-input">
                    <button class="btn btn-primary query-btn">查询</button>
                </div>
            </div>
        </div>
    `);

    // 添加扫描器样式
    const style = document.createElement('style');
    style.textContent = `
        .qr-scanner {
            text-align: center;
            padding: 20px;
        }
        .scanner-area {
            margin-bottom: 30px;
        }
        .scanner-frame {
            width: 200px;
            height: 200px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            background: #f8f9fa;
        }
        .scanner-frame i {
            font-size: 48px;
            color: #667eea;
            margin-bottom: 10px;
        }
        .manual-input h4 {
            margin-bottom: 15px;
            color: #333;
        }
        .input-group {
            display: flex;
            gap: 10px;
            max-width: 300px;
            margin: 0 auto;
        }
        .qr-input {
            flex: 1;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
    `;
    document.head.appendChild(style);

    modal.querySelector('.query-btn').addEventListener('click', function() {
        const qrCode = modal.querySelector('.qr-input').value;
        if (qrCode) {
            Utils.showMessage(`正在查询编号: ${qrCode}`, 'info');
            closeModal(modal);
            // 这里可以添加实际的查询逻辑
        } else {
            Utils.showMessage('请输入编号', 'warning');
        }
    });
}

// 生成质量报告
function generateQualityReport() {
    Utils.showMessage('正在生成质量报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('质量报告生成成功，已下载到本地', 'success');
    }, 2000);
}

// 显示风险分析
function showRiskAnalysis() {
    Utils.showMessage('正在进行风险分析...', 'info');
    // 模拟风险分析过程
    setTimeout(() => {
        Utils.showMessage('风险分析完成，发现2个高风险项目', 'warning');
    }, 1500);
}

// 处理项目操作
function handleProjectAction(action, projectName) {
    switch(action) {
        case '查看详情':
            Utils.showMessage(`正在查看"${projectName}"详细信息...`, 'info');
            break;
        case '进度更新':
            Utils.showMessage(`正在更新"${projectName}"进度...`, 'info');
            break;
        case '风险分析':
            Utils.showMessage(`正在分析"${projectName}"风险...`, 'info');
            break;
    }
}

// 处理风险操作
function handleRiskAction(action, riskTitle) {
    switch(action) {
        case '立即处理':
            Utils.showMessage(`正在处理风险: ${riskTitle}`, 'info');
            break;
        case '关注处理':
            Utils.showMessage(`已将风险加入关注列表: ${riskTitle}`, 'success');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看风险详情: ${riskTitle}`, 'info');
            break;
    }
}

// 初始化搜索功能
function initSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const keyword = this.value.toLowerCase();
            performSearch(keyword);
        });
    });
}

// 执行搜索
function performSearch(keyword) {
    if (!keyword.trim()) return;
    
    Utils.showMessage(`正在搜索"${keyword}"...`, 'info');
    // 这里可以添加实际的搜索逻辑
}

// 初始化文件树
function initFileTree() {
    const folderHeaders = document.querySelectorAll('.folder-header');
    
    folderHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const folderItem = this.closest('.folder-item');
            const folderContent = folderItem.querySelector('.folder-content');
            const icon = this.querySelector('i');
            
            if (folderContent) {
                if (folderContent.style.display === 'none' || !folderContent.style.display) {
                    folderContent.style.display = 'block';
                    icon.className = 'fas fa-folder-open';
                } else {
                    folderContent.style.display = 'none';
                    icon.className = 'fas fa-folder';
                }
            }
        });
    });
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateEngineeringData, 30000);
}

// 更新工程数据
function updateEngineeringData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number && !isNaN(parseInt(number.textContent))) {
            // 随机变化数据
            const currentValue = parseInt(number.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
            const newValue = Math.max(0, currentValue + change);
            
            if (newValue !== currentValue) {
                number.textContent = newValue;
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 标签页初始化函数
function initMonitoringTab() {
    // 项目监管标签页初始化
}

function initQualityTab() {
    // 质量评估标签页初始化
}

function initHiddenWorksTab() {
    // 隐蔽工程标签页初始化
}

function initArchivesTab() {
    // 工程档案标签页初始化
}

function initRiskTab() {
    // 风险预警标签页初始化
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 20px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    const cancelBtn = modal.querySelector('.cancel-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
