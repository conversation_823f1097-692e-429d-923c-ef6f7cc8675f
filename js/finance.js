// 财务管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化菜单切换
    initMenuToggle();
    
    // 初始化标签页
    initTabs();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
});

// 初始化菜单切换功能
function initMenuToggle() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
        });
    }
}

// 初始化标签页
function initTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

// 初始化图表
function initCharts() {
    // 月度支出趋势图表
    initExpenseChart();
    
    // 支出类型分布图表
    initExpenseTypeChart();
    
    // 预算执行对比图表
    initBudgetComparisonChart();
}

// 月度支出趋势图表
function initExpenseChart() {
    const ctx = document.getElementById('expenseChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '总支出',
                data: [2650000, 2890000, 2750000, 3120000, 2950000, 2856420],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '预算',
                data: [3000000, 3000000, 3000000, 3000000, 3000000, 3000000],
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                tension: 0.4,
                fill: false,
                borderDash: [5, 5]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + (value / 1000000) + 'M';
                        }
                    }
                }
            }
        }
    });
}

// 支出类型分布图表
function initExpenseTypeChart() {
    const ctx = document.getElementById('expenseTypeChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['设备维护', '物业管理', '餐饮服务', '水电费', '人员工资', '其他'],
            datasets: [{
                data: [25, 20, 18, 15, 12, 10],
                backgroundColor: [
                    '#667eea',
                    '#f093fb',
                    '#ffeaa7',
                    '#fd79a8',
                    '#a29bfe',
                    '#6c5ce7'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

// 预算执行对比图表
function initBudgetComparisonChart() {
    const ctx = document.getElementById('budgetComparisonChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['维修部门', '物业管理', '餐饮服务', '宿舍管理', '车辆管理'],
            datasets: [{
                label: '预算金额',
                data: [250000, 180000, 320000, 150000, 120000],
                backgroundColor: 'rgba(102, 126, 234, 0.3)',
                borderColor: '#667eea',
                borderWidth: 1
            }, {
                label: '实际支出',
                data: [285600, 145200, 298500, 135800, 98600],
                backgroundColor: 'rgba(240, 147, 251, 0.3)',
                borderColor: '#f093fb',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建预算按钮
    const newBudgetBtn = document.getElementById('newBudgetBtn');
    if (newBudgetBtn) {
        newBudgetBtn.addEventListener('click', function() {
            showModal('新建预算', createNewBudgetForm());
        });
    }
    
    // 导出报表按钮
    const exportReportBtn = document.getElementById('exportReportBtn');
    if (exportReportBtn) {
        exportReportBtn.addEventListener('click', function() {
            exportFinancialReport();
        });
    }
    
    // 新建预算按钮（预算管理页面）
    const createBudgetBtn = document.getElementById('createBudgetBtn');
    if (createBudgetBtn) {
        createBudgetBtn.addEventListener('click', function() {
            showModal('新建预算', createNewBudgetForm());
        });
    }
    
    // 预算模板按钮
    const budgetTemplateBtn = document.getElementById('budgetTemplateBtn');
    if (budgetTemplateBtn) {
        budgetTemplateBtn.addEventListener('click', function() {
            showModal('预算模板', createBudgetTemplateForm());
        });
    }
    
    // 新建费用申请按钮
    const newExpenseBtn = document.getElementById('newExpenseBtn');
    if (newExpenseBtn) {
        newExpenseBtn.addEventListener('click', function() {
            showModal('新建费用申请', createNewExpenseForm());
        });
    }
}

// 显示模态框
function showModal(title, content) {
    // 创建模态框HTML
    const modalHTML = `
        <div class="modal-overlay" id="modalOverlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button class="btn btn-primary" onclick="submitModal()">确定</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 显示模态框
    document.getElementById('modalOverlay').style.display = 'flex';
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('modalOverlay');
    if (modal) {
        modal.remove();
    }
}

// 提交模态框
function submitModal() {
    // 这里可以添加具体的提交逻辑
    alert('操作成功！');
    closeModal();
}

// 创建新建预算表单
function createNewBudgetForm() {
    return `
        <div class="form-container">
            <div class="form-row">
                <div class="form-group">
                    <label>预算部门</label>
                    <select class="form-control">
                        <option>维修部门</option>
                        <option>物业管理</option>
                        <option>餐饮服务</option>
                        <option>宿舍管理</option>
                        <option>车辆管理</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>预算项目</label>
                    <input type="text" class="form-control" placeholder="请输入预算项目名称">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>预算金额</label>
                    <input type="number" class="form-control" placeholder="请输入预算金额">
                </div>
                <div class="form-group">
                    <label>预算周期</label>
                    <select class="form-control">
                        <option>月度预算</option>
                        <option>季度预算</option>
                        <option>年度预算</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>预算说明</label>
                <textarea class="form-control" rows="3" placeholder="请输入预算说明"></textarea>
            </div>
        </div>
    `;
}

// 创建预算模板表单
function createBudgetTemplateForm() {
    return `
        <div class="template-list">
            <div class="template-item">
                <h4>设备维护预算模板</h4>
                <p>包含设备维修、保养、更换等费用项目</p>
                <div class="template-actions">
                    <button class="btn btn-sm btn-secondary">编辑</button>
                    <button class="btn btn-sm btn-primary">使用</button>
                </div>
            </div>
            <div class="template-item">
                <h4>物业管理预算模板</h4>
                <p>包含清洁、安保、绿化等物业服务费用</p>
                <div class="template-actions">
                    <button class="btn btn-sm btn-secondary">编辑</button>
                    <button class="btn btn-sm btn-primary">使用</button>
                </div>
            </div>
            <div class="template-item">
                <h4>餐饮服务预算模板</h4>
                <p>包含食材采购、人员工资、设备维护等费用</p>
                <div class="template-actions">
                    <button class="btn btn-sm btn-secondary">编辑</button>
                    <button class="btn btn-sm btn-primary">使用</button>
                </div>
            </div>
        </div>
    `;
}

// 创建新建费用申请表单
function createNewExpenseForm() {
    return `
        <div class="form-container">
            <div class="form-row">
                <div class="form-group">
                    <label>申请部门</label>
                    <select class="form-control">
                        <option>维修部门</option>
                        <option>物业管理</option>
                        <option>餐饮服务</option>
                        <option>宿舍管理</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>费用类型</label>
                    <select class="form-control">
                        <option>设备维修</option>
                        <option>物料采购</option>
                        <option>服务费用</option>
                        <option>其他费用</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>申请金额</label>
                    <input type="number" class="form-control" placeholder="请输入申请金额">
                </div>
                <div class="form-group">
                    <label>紧急程度</label>
                    <select class="form-control">
                        <option>普通</option>
                        <option>紧急</option>
                        <option>特急</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>费用说明</label>
                <textarea class="form-control" rows="3" placeholder="请详细说明费用用途"></textarea>
            </div>
            <div class="form-group">
                <label>附件上传</label>
                <input type="file" class="form-control" multiple accept=".pdf,.jpg,.png,.doc,.docx">
            </div>
        </div>
    `;
}

// 导出财务报表
function exportFinancialReport() {
    // 模拟导出功能
    const reportData = {
        exportTime: new Date().toLocaleString(),
        totalExpense: '¥2,856,420',
        budgetExecution: '87.2%',
        pendingApprovals: 15,
        supplierReconciliation: '98.5%'
    };
    
    console.log('导出财务报表数据:', reportData);
    alert('财务报表导出成功！');
}
