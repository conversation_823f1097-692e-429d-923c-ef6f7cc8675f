// 信息发布管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化菜单切换
    initMenuToggle();
    
    // 初始化标签页
    initTabs();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
});

// 初始化菜单切换功能
function initMenuToggle() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
        });
    }
}

// 初始化标签页
function initTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

// 初始化图表
function initCharts() {
    // 发布趋势图表
    initPublishTrendChart();
    
    // 内容类型分布图表
    initContentTypeChart();
    
    // 部门阅读率图表
    initDepartmentReadingChart();
    
    // 阅读量趋势图表
    initReadingTrendChart();
}

// 发布趋势图表
function initPublishTrendChart() {
    const ctx = document.getElementById('publishTrendChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '发布数量',
                data: [45, 52, 38, 65, 59, 72],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '阅读量',
                data: [1200, 1450, 980, 1680, 1520, 1890],
                borderColor: '#f093fb',
                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                tension: 0.4,
                fill: true,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
}

// 内容类型分布图表
function initContentTypeChart() {
    const ctx = document.getElementById('contentTypeChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['图文内容', '视频内容', '文档资料', '音频内容'],
            datasets: [{
                data: [68.6, 18.8, 12.6, 5.2],
                backgroundColor: [
                    '#667eea',
                    '#f093fb',
                    '#ffeaa7',
                    '#fd79a8'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

// 部门阅读率图表
function initDepartmentReadingChart() {
    const ctx = document.getElementById('departmentReadingChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['医务部', '护理部', '后勤部', '财务部', '人事部', '信息科'],
            datasets: [{
                label: '阅读率 (%)',
                data: [92, 88, 85, 78, 82, 90],
                backgroundColor: [
                    '#667eea',
                    '#f093fb',
                    '#ffeaa7',
                    '#fd79a8',
                    '#a29bfe',
                    '#6c5ce7'
                ],
                borderRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// 阅读量趋势图表
function initReadingTrendChart() {
    const ctx = document.getElementById('readingTrendChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [{
                label: '阅读量',
                data: [1200, 1900, 1500, 1800, 2100, 800, 600],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '互动量',
                data: [180, 290, 220, 260, 320, 120, 90],
                borderColor: '#f093fb',
                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
}

// 初始化按钮事件
function initButtonEvents() {
    // 发布信息按钮
    const newInfoBtn = document.getElementById('newInfoBtn');
    if (newInfoBtn) {
        newInfoBtn.addEventListener('click', function() {
            showModal('新建信息发布', createNewInfoForm());
        });
    }
    
    // 模板管理按钮
    const templateBtn = document.getElementById('templateBtn');
    if (templateBtn) {
        templateBtn.addEventListener('click', function() {
            showModal('模板管理', createTemplateManagementForm());
        });
    }
    
    // 新建投放规则按钮
    const createTargetingBtn = document.getElementById('createTargetingBtn');
    if (createTargetingBtn) {
        createTargetingBtn.addEventListener('click', function() {
            showModal('新建投放规则', createTargetingRuleForm());
        });
    }
    
    // 新建定时发布按钮
    const schedulePostBtn = document.getElementById('schedulePostBtn');
    if (schedulePostBtn) {
        schedulePostBtn.addEventListener('click', function() {
            showModal('新建定时发布', createSchedulePostForm());
        });
    }
    
    // 导出报告按钮
    const exportReportBtn = document.getElementById('exportReportBtn');
    if (exportReportBtn) {
        exportReportBtn.addEventListener('click', function() {
            exportAnalyticsReport();
        });
    }
}

// 显示模态框
function showModal(title, content) {
    // 创建模态框HTML
    const modalHTML = `
        <div class="modal-overlay" id="modalOverlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button class="btn btn-primary" onclick="submitModal()">确定</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 显示模态框
    document.getElementById('modalOverlay').style.display = 'flex';
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('modalOverlay');
    if (modal) {
        modal.remove();
    }
}

// 提交模态框
function submitModal() {
    // 这里可以添加具体的提交逻辑
    alert('功能提交成功！');
    closeModal();
}

// 创建新信息发布表单
function createNewInfoForm() {
    return `
        <div class="form-container">
            <div class="form-group">
                <label>信息标题</label>
                <input type="text" class="form-control" placeholder="请输入信息标题">
            </div>
            <div class="form-group">
                <label>内容类型</label>
                <select class="form-control">
                    <option>通知公告</option>
                    <option>新闻资讯</option>
                    <option>培训资料</option>
                    <option>政策法规</option>
                </select>
            </div>
            <div class="form-group">
                <label>优先级</label>
                <select class="form-control">
                    <option>普通</option>
                    <option>重要</option>
                    <option>紧急</option>
                </select>
            </div>
            <div class="form-group">
                <label>目标部门</label>
                <div class="checkbox-group">
                    <label><input type="checkbox"> 全院</label>
                    <label><input type="checkbox"> 医务部</label>
                    <label><input type="checkbox"> 护理部</label>
                    <label><input type="checkbox"> 后勤部</label>
                </div>
            </div>
        </div>
    `;
}

// 创建模板管理表单
function createTemplateManagementForm() {
    return `
        <div class="template-list">
            <div class="template-item">
                <h4>通知公告模板</h4>
                <p>标准通知公告格式模板</p>
                <div class="template-actions">
                    <button class="btn btn-sm btn-secondary">编辑</button>
                    <button class="btn btn-sm btn-primary">使用</button>
                </div>
            </div>
            <div class="template-item">
                <h4>培训通知模板</h4>
                <p>培训活动通知格式模板</p>
                <div class="template-actions">
                    <button class="btn btn-sm btn-secondary">编辑</button>
                    <button class="btn btn-sm btn-primary">使用</button>
                </div>
            </div>
        </div>
    `;
}

// 创建投放规则表单
function createTargetingRuleForm() {
    return `
        <div class="form-container">
            <div class="form-group">
                <label>规则名称</label>
                <input type="text" class="form-control" placeholder="请输入规则名称">
            </div>
            <div class="form-group">
                <label>内容类型</label>
                <select class="form-control">
                    <option>通知公告</option>
                    <option>培训通知</option>
                    <option>紧急通知</option>
                </select>
            </div>
            <div class="form-group">
                <label>目标部门</label>
                <div class="checkbox-group">
                    <label><input type="checkbox"> 全院科室</label>
                    <label><input type="checkbox"> 医务部</label>
                    <label><input type="checkbox"> 护理部</label>
                </div>
            </div>
            <div class="form-group">
                <label>推送方式</label>
                <div class="checkbox-group">
                    <label><input type="checkbox"> 系统通知</label>
                    <label><input type="checkbox"> 邮件推送</label>
                    <label><input type="checkbox"> 短信通知</label>
                </div>
            </div>
        </div>
    `;
}

// 创建定时发布表单
function createSchedulePostForm() {
    return `
        <div class="form-container">
            <div class="form-group">
                <label>发布标题</label>
                <input type="text" class="form-control" placeholder="请输入发布标题">
            </div>
            <div class="form-group">
                <label>发布时间</label>
                <input type="datetime-local" class="form-control">
            </div>
            <div class="form-group">
                <label>目标受众</label>
                <select class="form-control">
                    <option>全院职工</option>
                    <option>科室主任</option>
                    <option>护士长</option>
                    <option>管理人员</option>
                </select>
            </div>
            <div class="form-group">
                <label>推送渠道</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" checked> 系统通知</label>
                    <label><input type="checkbox"> 邮件推送</label>
                    <label><input type="checkbox"> 短信通知</label>
                </div>
            </div>
        </div>
    `;
}

// 导出分析报告
function exportAnalyticsReport() {
    // 模拟导出功能
    const reportData = {
        exportTime: new Date().toLocaleString(),
        totalPublications: 1248,
        totalReads: 45892,
        coverageRate: '89.2%',
        interactionRate: '76.8%'
    };
    
    console.log('导出报告数据:', reportData);
    alert('报告导出成功！');
}
