// 巡检管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页
    initTabs();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
});

// 初始化标签页
function initTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'overview':
                    initOverviewTab();
                    break;
                case 'planning':
                    initPlanningTab();
                    break;
                case 'ai-detection':
                    initAIDetectionTab();
                    break;
                case 'hazard-management':
                    initHazardTab();
                    break;
                case 'equipment-health':
                    initHealthTab();
                    break;
                case 'efficiency-analysis':
                    initEfficiencyTab();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 巡检完成趋势图
    const trendCtx = document.getElementById('inspectionTrendChart');
    if (trendCtx) {
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '完成任务',
                    data: [28, 32, 25, 30, 35, 22, 18],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '发现问题',
                    data: [3, 5, 2, 4, 6, 3, 2],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 隐患类型分布图
    const hazardCtx = document.getElementById('hazardTypeChart');
    if (hazardCtx) {
        new Chart(hazardCtx, {
            type: 'doughnut',
            data: {
                labels: ['设备故障', '安全隐患', '环境问题', '维护不当', '其他'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: ['#e74c3c', '#f39c12', '#3498db', '#27ae60', '#9b59b6'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // 设备健康分布图
    const healthCtx = document.getElementById('healthDistributionChart');
    if (healthCtx) {
        new Chart(healthCtx, {
            type: 'pie',
            data: {
                labels: ['优秀', '良好', '警告', '危险'],
                datasets: [{
                    data: [156, 89, 23, 8],
                    backgroundColor: ['#27ae60', '#3498db', '#f39c12', '#e74c3c'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // 效率趋势图
    const efficiencyCtx = document.getElementById('efficiencyTrendChart');
    if (efficiencyCtx) {
        new Chart(efficiencyCtx, {
            type: 'bar',
            data: {
                labels: ['第1周', '第2周', '第3周', '第4周'],
                datasets: [{
                    label: '完成率(%)',
                    data: [92, 94, 91, 96],
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: '#667eea',
                    borderWidth: 1
                }, {
                    label: '问题发现率(%)',
                    data: [15, 12, 18, 10],
                    backgroundColor: 'rgba(231, 76, 60, 0.8)',
                    borderColor: '#e74c3c',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 问题高发区域图
    const problemAreaCtx = document.getElementById('problemAreaChart');
    if (problemAreaCtx) {
        new Chart(problemAreaCtx, {
            type: 'radar',
            data: {
                labels: ['配电房', '消防设备', '电梯', '空调系统', '给排水', '照明系统'],
                datasets: [{
                    label: '问题发生频率',
                    data: [25, 18, 12, 15, 8, 10],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.2)',
                    pointBackgroundColor: '#e74c3c',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#e74c3c'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建巡检任务
    const newInspectionBtn = document.getElementById('newInspectionBtn');
    if (newInspectionBtn) {
        newInspectionBtn.addEventListener('click', function() {
            showInspectionForm();
        });
    }

    // 生成报告
    const generateReportBtn = document.getElementById('generateReportBtn');
    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', function() {
            generateInspectionReport();
        });
    }

    // 智能优化路径
    const optimizeRouteBtn = document.getElementById('optimizeRouteBtn');
    if (optimizeRouteBtn) {
        optimizeRouteBtn.addEventListener('click', function() {
            optimizeInspectionRoute();
        });
    }

    // 上传图片
    const uploadImageBtn = document.getElementById('uploadImageBtn');
    if (uploadImageBtn) {
        uploadImageBtn.addEventListener('click', function() {
            triggerImageUpload();
        });
    }

    // 开始录音
    const startRecordingBtn = document.getElementById('startRecordingBtn');
    if (startRecordingBtn) {
        startRecordingBtn.addEventListener('click', function() {
            toggleRecording();
        });
    }

    // 上报隐患
    const reportHazardBtn = document.getElementById('reportHazardBtn');
    if (reportHazardBtn) {
        reportHazardBtn.addEventListener('click', function() {
            showHazardForm();
        });
    }

    // 生成优化建议
    const generateSuggestionsBtn = document.getElementById('generateSuggestionsBtn');
    if (generateSuggestionsBtn) {
        generateSuggestionsBtn.addEventListener('click', function() {
            generateOptimizationSuggestions();
        });
    }
}

// 显示巡检任务表单
function showInspectionForm() {
    const modal = createModal('新建巡检任务', `
        <form class="inspection-form">
            <div class="form-row">
                <div class="form-group">
                    <label>巡检区域 *</label>
                    <select name="area" required>
                        <option value="">请选择区域</option>
                        <option value="power">配电房</option>
                        <option value="fire">消防设备</option>
                        <option value="elevator">电梯系统</option>
                        <option value="hvac">空调系统</option>
                        <option value="water">给排水</option>
                        <option value="lighting">照明系统</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>巡检人员 *</label>
                    <select name="inspector" required>
                        <option value="">请选择人员</option>
                        <option value="zhang">张师傅</option>
                        <option value="li">李师傅</option>
                        <option value="wang">王师傅</option>
                        <option value="liu">刘师傅</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>巡检类型 *</label>
                    <select name="type" required>
                        <option value="">请选择类型</option>
                        <option value="routine">日常巡检</option>
                        <option value="special">专项巡检</option>
                        <option value="emergency">应急巡检</option>
                        <option value="maintenance">维护巡检</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>计划时间 *</label>
                    <input type="datetime-local" name="scheduledTime" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>优先级 *</label>
                    <select name="priority" required>
                        <option value="">请选择优先级</option>
                        <option value="urgent">紧急</option>
                        <option value="high">高</option>
                        <option value="medium">中</option>
                        <option value="low">低</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>预计时长</label>
                    <input type="number" name="duration" min="10" max="480" placeholder="分钟">
                </div>
            </div>
            <div class="form-group">
                <label>巡检要点</label>
                <textarea name="checkpoints" placeholder="请输入具体巡检要点"></textarea>
            </div>
            <div class="form-group">
                <label>特殊要求</label>
                <textarea name="requirements" placeholder="请输入特殊要求"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">创建任务</button>
            </div>
        </form>
    `);

    modal.querySelector('.inspection-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const area = e.target.area.value;
        const inspector = e.target.inspector.value;
        Utils.showMessage(`巡检任务已创建，区域: ${area}，巡检员: ${inspector}`, 'success');
        closeModal(modal);
    });
}

// 显示隐患上报表单
function showHazardForm() {
    const modal = createModal('上报安全隐患', `
        <form class="hazard-form">
            <div class="form-row">
                <div class="form-group">
                    <label>隐患位置 *</label>
                    <input type="text" name="location" required placeholder="请输入具体位置">
                </div>
                <div class="form-group">
                    <label>隐患类型 *</label>
                    <select name="type" required>
                        <option value="">请选择类型</option>
                        <option value="equipment">设备故障</option>
                        <option value="safety">安全隐患</option>
                        <option value="environment">环境问题</option>
                        <option value="maintenance">维护不当</option>
                        <option value="other">其他</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>严重程度 *</label>
                    <select name="severity" required>
                        <option value="">请选择程度</option>
                        <option value="urgent">紧急</option>
                        <option value="high">高</option>
                        <option value="medium">中</option>
                        <option value="low">低</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>发现时间 *</label>
                    <input type="datetime-local" name="discoveryTime" required>
                </div>
            </div>
            <div class="form-group">
                <label>隐患描述 *</label>
                <textarea name="description" required placeholder="请详细描述隐患情况"></textarea>
            </div>
            <div class="form-group">
                <label>现场照片</label>
                <div class="upload-area">
                    <input type="file" name="photos" multiple accept="image/*" style="display: none;">
                    <div class="upload-placeholder">
                        <i class="fas fa-camera"></i>
                        <p>点击上传现场照片</p>
                        <span>支持多张图片上传</span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label>建议措施</label>
                <textarea name="suggestions" placeholder="请输入建议的处理措施"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-danger">上报隐患</button>
            </div>
        </form>
    `);

    modal.querySelector('.hazard-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const location = e.target.location.value;
        const type = e.target.type.value;
        Utils.showMessage(`隐患已上报，位置: ${location}，类型: ${type}`, 'success');
        closeModal(modal);
    });
}

// 生成巡检报告
function generateInspectionReport() {
    Utils.showMessage('正在生成巡检报告...', 'info');
    setTimeout(() => {
        Utils.showMessage('巡检报告已生成并发送到邮箱', 'success');
    }, 2000);
}

// 优化巡检路径
function optimizeInspectionRoute() {
    Utils.showMessage('正在进行智能路径优化...', 'info');
    setTimeout(() => {
        Utils.showMessage('路径优化完成，预计节省时间15%', 'success');
    }, 3000);
}

// 触发图片上传
function triggerImageUpload() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file) {
            Utils.showMessage('正在进行AI识别分析...', 'info');
            setTimeout(() => {
                Utils.showMessage('AI识别完成，发现1个潜在问题', 'warning');
            }, 2000);
        }
    };
    input.click();
}

// 切换录音状态
function toggleRecording() {
    const btn = document.getElementById('startRecordingBtn');
    const isRecording = btn.classList.contains('recording');

    if (isRecording) {
        btn.classList.remove('recording');
        btn.innerHTML = '<i class="fas fa-microphone"></i> 开始录音';
        btn.classList.remove('btn-danger');
        btn.classList.add('btn-success');
        Utils.showMessage('录音已停止，正在转录...', 'info');
        setTimeout(() => {
            Utils.showMessage('语音转录完成，AI分析结果已生成', 'success');
        }, 2000);
    } else {
        btn.classList.add('recording');
        btn.innerHTML = '<i class="fas fa-stop"></i> 停止录音';
        btn.classList.remove('btn-success');
        btn.classList.add('btn-danger');
        Utils.showMessage('开始录音...', 'info');
    }
}

// 生成优化建议
function generateOptimizationSuggestions() {
    Utils.showMessage('正在分析巡检数据，生成优化建议...', 'info');
    setTimeout(() => {
        Utils.showMessage('优化建议已生成，发现3个改进点', 'success');
    }, 3000);
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-container">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 关闭模态框事件
    modal.querySelector('.modal-close').addEventListener('click', () => closeModal(modal));
    modal.querySelector('.cancel-btn').addEventListener('click', () => closeModal(modal));
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal(modal);
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}

// 标签页初始化函数
function initOverviewTab() {
    // 概览标签页初始化
}

function initPlanningTab() {
    // 路径规划标签页初始化
}

function initAIDetectionTab() {
    // AI识别标签页初始化
}

function initHazardTab() {
    // 隐患管理标签页初始化
}

function initHealthTab() {
    // 设备健康标签页初始化
    // 重新初始化健康趋势图表
    setTimeout(() => {
        initHealthTrendCharts();
    }, 100);
}

function initEfficiencyTab() {
    // 效能分析标签页初始化
}

// 初始化设备健康趋势图表
function initHealthTrendCharts() {
    const healthTrend1 = document.getElementById('healthTrend1');
    if (healthTrend1) {
        new Chart(healthTrend1, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    data: [88, 90, 89, 92, 91, 92],
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointRadius: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        display: false,
                        min: 80,
                        max: 100
                    },
                    x: {
                        display: false
                    }
                }
            }
        });
    }
}
