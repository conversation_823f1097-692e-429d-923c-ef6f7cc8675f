// 报修管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化智能诊断
    initDiagnosis();
    
    // 初始化维修调度
    initScheduling();
    
    // 初始化搜索功能
    initSearch();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'diagnosis':
                    initDiagnosisTab();
                    break;
                case 'scheduling':
                    initSchedulingTab();
                    break;
                case 'evaluation':
                    initEvaluationTab();
                    break;
                case 'prevention':
                    initPreventionTab();
                    break;
                case 'knowledge':
                    initKnowledgeTab();
                    break;
            }
        });
    });
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建报修单
    const newRepairBtn = document.getElementById('newRepairBtn');
    if (newRepairBtn) {
        newRepairBtn.addEventListener('click', function() {
            showRepairForm();
        });
    }

    // 预防性维护
    const maintenanceBtn = document.getElementById('maintenanceBtn');
    if (maintenanceBtn) {
        maintenanceBtn.addEventListener('click', function() {
            showMaintenanceForm();
        });
    }

    // 维修报表
    const reportBtn = document.getElementById('reportBtn');
    if (reportBtn) {
        reportBtn.addEventListener('click', function() {
            generateReport();
        });
    }

    // 智能分配按钮
    const autoAssignBtn = document.querySelector('.auto-assign-btn');
    if (autoAssignBtn) {
        autoAssignBtn.addEventListener('click', function() {
            autoAssignTasks();
        });
    }
}

// 初始化智能诊断
function initDiagnosis() {
    const diagnoseBtn = document.querySelector('.diagnose-btn');
    if (diagnoseBtn) {
        diagnoseBtn.addEventListener('click', function() {
            performDiagnosis();
        });
    }
}

// 执行智能诊断
function performDiagnosis() {
    const equipmentType = document.querySelector('.equipment-select').value;
    const equipmentCode = document.querySelector('.equipment-code').value;
    const faultDescription = document.querySelector('.fault-description').value;

    if (!equipmentType || !faultDescription) {
        Utils.showMessage('请填写设备类型和故障描述', 'warning');
        return;
    }

    // 显示诊断中状态
    const diagnoseBtn = document.querySelector('.diagnose-btn');
    const originalText = diagnoseBtn.innerHTML;
    diagnoseBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 诊断中...';
    diagnoseBtn.disabled = true;

    // 模拟AI诊断过程
    setTimeout(() => {
        // 恢复按钮状态
        diagnoseBtn.innerHTML = originalText;
        diagnoseBtn.disabled = false;

        // 显示诊断结果
        showDiagnosisResult(equipmentType, faultDescription);
        Utils.showMessage('智能诊断完成', 'success');
    }, 2000);
}

// 显示诊断结果
function showDiagnosisResult(equipmentType, faultDescription) {
    // 根据设备类型和故障描述生成模拟结果
    const results = {
        'projector': {
            faults: [
                { probability: '90%', desc: '投影仪灯泡老化或损坏' },
                { probability: '60%', desc: '投影仪散热系统故障' },
                { probability: '30%', desc: '电源适配器问题' }
            ],
            solutions: [
                { step: 1, title: '检查灯泡状态', desc: '查看投影仪灯泡使用时间，如超过2000小时建议更换' },
                { step: 2, title: '清洁散热系统', desc: '清理投影仪内部灰尘，检查散热风扇是否正常工作' },
                { step: 3, title: '联系专业维修', desc: '如以上步骤无效，建议联系专业维修人员进行检修' }
            ]
        },
        'computer': {
            faults: [
                { probability: '85%', desc: '硬盘故障或损坏' },
                { probability: '70%', desc: '内存条接触不良' },
                { probability: '40%', desc: '主板电源问题' }
            ],
            solutions: [
                { step: 1, title: '检查硬盘状态', desc: '运行硬盘检测工具，查看是否有坏道' },
                { step: 2, title: '重新插拔内存', desc: '关机后重新插拔内存条，清洁金手指' },
                { step: 3, title: '检查电源供应', desc: '测试电源输出是否正常，必要时更换电源' }
            ]
        }
    };

    const result = results[equipmentType] || results['projector'];
    
    // 更新置信度
    const confidenceScore = Math.floor(Math.random() * 20) + 80; // 80-99%
    document.querySelector('.score-fill').style.width = confidenceScore + '%';
    document.querySelector('.confidence-score span:last-child').textContent = confidenceScore + '%';

    // 更新故障分析
    const faultList = document.querySelector('.fault-list');
    faultList.innerHTML = '';
    result.faults.forEach(fault => {
        const li = document.createElement('li');
        li.innerHTML = `
            <span class="probability">${fault.probability}</span>
            <span class="fault-desc">${fault.desc}</span>
        `;
        faultList.appendChild(li);
    });

    // 更新解决方案
    const solutionSteps = document.querySelector('.solution-steps');
    solutionSteps.innerHTML = '';
    result.solutions.forEach(solution => {
        const stepDiv = document.createElement('div');
        stepDiv.className = 'step';
        stepDiv.innerHTML = `
            <div class="step-number">${solution.step}</div>
            <div class="step-content">
                <h6>${solution.title}</h6>
                <p>${solution.desc}</p>
            </div>
        `;
        solutionSteps.appendChild(stepDiv);
    });
}

// 初始化维修调度
function initScheduling() {
    // 分配按钮事件
    const assignBtns = document.querySelectorAll('.order-actions .btn-primary');
    assignBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const orderItem = this.closest('.order-item');
            const select = orderItem.querySelector('.assign-select');
            const selectedTechnician = select.value;
            
            if (!selectedTechnician) {
                Utils.showMessage('请选择维修人员', 'warning');
                return;
            }
            
            assignTask(orderItem, selectedTechnician);
        });
    });
}

// 分配任务
function assignTask(orderItem, technician) {
    const orderTitle = orderItem.querySelector('h4').textContent;
    const technicianName = orderItem.querySelector(`option[value="${technician}"]`).textContent;
    
    Utils.showMessage(`已将"${orderTitle}"分配给${technicianName}`, 'success');
    
    // 移除工单
    orderItem.style.opacity = '0.5';
    setTimeout(() => {
        orderItem.remove();
    }, 1000);
    
    // 更新技师工作负载
    updateTechnicianWorkload(technician);
}

// 自动分配任务
function autoAssignTasks() {
    const orderItems = document.querySelectorAll('.order-item');
    let assignedCount = 0;
    
    orderItems.forEach((item, index) => {
        setTimeout(() => {
            const select = item.querySelector('.assign-select');
            const options = select.querySelectorAll('option[value]');
            
            if (options.length > 0) {
                // 选择第一个可用的技师
                const randomTechnician = options[Math.floor(Math.random() * options.length)].value;
                select.value = randomTechnician;
                assignTask(item, randomTechnician);
                assignedCount++;
            }
        }, index * 500);
    });
    
    setTimeout(() => {
        Utils.showMessage(`智能分配完成，共分配${assignedCount}个工单`, 'success');
    }, orderItems.length * 500 + 500);
}

// 更新技师工作负载
function updateTechnicianWorkload(technicianId) {
    const technicianCards = document.querySelectorAll('.technician-card');
    
    technicianCards.forEach(card => {
        const workloadText = card.querySelector('.workload span');
        const workloadBar = card.querySelector('.workload-fill');
        
        if (workloadText) {
            const currentLoad = parseInt(workloadText.textContent.match(/\d+/)[0]);
            const maxLoad = parseInt(workloadText.textContent.match(/\/(\d+)/)[1]);
            
            if (currentLoad < maxLoad) {
                const newLoad = currentLoad + 1;
                workloadText.textContent = `工作负载: ${newLoad}/${maxLoad}`;
                workloadBar.style.width = (newLoad / maxLoad * 100) + '%';
                
                // 更新状态
                if (newLoad >= maxLoad * 0.8) {
                    card.classList.remove('available');
                    card.classList.add('busy');
                    card.querySelector('.technician-status-badge').textContent = '忙碌';
                    card.querySelector('.technician-status-badge').className = 'technician-status-badge busy';
                }
            }
        }
    });
}

// 显示报修表单
function showRepairForm() {
    const modal = createModal('新建报修单', `
        <form class="repair-form">
            <div class="form-row">
                <div class="form-group">
                    <label>报修人 *</label>
                    <input type="text" name="reporter" required placeholder="请输入报修人姓名">
                </div>
                <div class="form-group">
                    <label>联系电话 *</label>
                    <input type="tel" name="phone" required placeholder="请输入联系电话">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>设备类型 *</label>
                    <select name="equipmentType" required>
                        <option value="">请选择设备类型</option>
                        <option value="computer">计算机设备</option>
                        <option value="printer">打印设备</option>
                        <option value="projector">投影设备</option>
                        <option value="air-conditioner">空调设备</option>
                        <option value="elevator">电梯设备</option>
                        <option value="lighting">照明设备</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>设备编号</label>
                    <input type="text" name="equipmentCode" placeholder="请输入设备编号">
                </div>
            </div>
            <div class="form-group">
                <label>设备位置 *</label>
                <input type="text" name="location" required placeholder="请输入设备位置">
            </div>
            <div class="form-group">
                <label>故障描述 *</label>
                <textarea name="faultDescription" required placeholder="请详细描述故障现象"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>紧急程度 *</label>
                    <select name="urgency" required>
                        <option value="">请选择紧急程度</option>
                        <option value="low">一般</option>
                        <option value="medium">紧急</option>
                        <option value="high">非常紧急</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>期望完成时间</label>
                    <input type="datetime-local" name="expectedTime">
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交报修</button>
            </div>
        </form>
    `);

    modal.querySelector('.repair-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('报修单已提交，系统将自动分配维修人员', 'success');
        closeModal(modal);
    });
}

// 显示维护表单
function showMaintenanceForm() {
    const modal = createModal('预防性维护计划', `
        <form class="maintenance-form">
            <div class="form-group">
                <label>设备类型 *</label>
                <select name="equipmentType" required>
                    <option value="">请选择设备类型</option>
                    <option value="hvac">中央空调</option>
                    <option value="elevator">电梯设备</option>
                    <option value="fire">消防设备</option>
                    <option value="power">供电设备</option>
                    <option value="network">网络设备</option>
                </select>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>维护类型 *</label>
                    <select name="maintenanceType" required>
                        <option value="">请选择维护类型</option>
                        <option value="routine">例行保养</option>
                        <option value="inspection">安全检查</option>
                        <option value="cleaning">清洁维护</option>
                        <option value="calibration">校准调试</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>维护周期 *</label>
                    <select name="cycle" required>
                        <option value="">请选择维护周期</option>
                        <option value="weekly">每周</option>
                        <option value="monthly">每月</option>
                        <option value="quarterly">每季度</option>
                        <option value="yearly">每年</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>计划开始时间 *</label>
                    <input type="datetime-local" name="startTime" required>
                </div>
                <div class="form-group">
                    <label>预计耗时(小时)</label>
                    <input type="number" name="duration" placeholder="请输入预计耗时">
                </div>
            </div>
            <div class="form-group">
                <label>维护内容 *</label>
                <textarea name="content" required placeholder="请详细描述维护内容和要求"></textarea>
            </div>
            <div class="form-group">
                <label>负责人员</label>
                <select name="assignee">
                    <option value="">请选择负责人员</option>
                    <option value="li">李师傅</option>
                    <option value="wang">王师傅</option>
                    <option value="zhang">张师傅</option>
                </select>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">创建计划</button>
            </div>
        </form>
    `);

    modal.querySelector('.maintenance-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('维护计划已创建，系统将自动提醒', 'success');
        closeModal(modal);
    });
}

// 生成报表
function generateReport() {
    Utils.showMessage('正在生成维修管理报表...', 'info');
    // 模拟报表生成过程
    setTimeout(() => {
        Utils.showMessage('报表生成成功，已下载到本地', 'success');
    }, 2000);
}

// 初始化搜索功能
function initSearch() {
    const searchInputs = document.querySelectorAll('.knowledge-search-input');
    const searchBtns = document.querySelectorAll('.search-btn');

    searchInputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performKnowledgeSearch(this.value);
            }
        });
    });

    searchBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const input = this.previousElementSibling;
            performKnowledgeSearch(input.value);
        });
    });
}

// 执行知识库搜索
function performKnowledgeSearch(keyword) {
    if (!keyword.trim()) {
        Utils.showMessage('请输入搜索关键词', 'warning');
        return;
    }

    Utils.showMessage(`正在搜索"${keyword}"相关案例...`, 'info');
    
    // 模拟搜索过程
    setTimeout(() => {
        Utils.showMessage(`找到${Math.floor(Math.random() * 10) + 1}个相关案例`, 'success');
    }, 1000);
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateMaintenanceData, 30000);
}

// 更新维修数据
function updateMaintenanceData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number && !isNaN(parseInt(number.textContent))) {
            // 随机变化数据
            const currentValue = parseInt(number.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
            const newValue = Math.max(0, currentValue + change);
            
            if (newValue !== currentValue) {
                number.textContent = newValue;
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 标签页初始化函数
function initDiagnosisTab() {
    // 诊断标签页初始化
}

function initSchedulingTab() {
    // 调度标签页初始化
}

function initEvaluationTab() {
    // 评价标签页初始化
}

function initPreventionTab() {
    // 预防性维护标签页初始化
}

function initKnowledgeTab() {
    // 知识库标签页初始化
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 20px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    modal.querySelector('.cancel-btn').addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
