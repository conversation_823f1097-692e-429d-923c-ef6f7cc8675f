// 医废管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化筛选功能
    initFilters();
    
    // 初始化扫码功能
    initScanner();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'classification':
                    initClassificationTab();
                    break;
                case 'tracking':
                    initTrackingTab();
                    break;
                case 'compliance':
                    initComplianceTab();
                    break;
                case 'alerts':
                    initAlertsTab();
                    break;
                case 'environment':
                    initEnvironmentTab();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 环境指标趋势图
    const environmentCtx = document.getElementById('environmentChart');
    if (environmentCtx) {
        new Chart(environmentCtx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                datasets: [{
                    label: 'AQI',
                    data: [65, 68, 72, 70, 68, 66],
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'COD (mg/L)',
                    data: [42, 45, 48, 46, 44, 43],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 污染物排放图
    const emissionCtx = document.getElementById('emissionChart');
    if (emissionCtx) {
        new Chart(emissionCtx, {
            type: 'doughnut',
            data: {
                labels: ['SO₂', 'NOx', 'PM2.5', '其他'],
                datasets: [{
                    data: [25, 35, 20, 20],
                    backgroundColor: ['#e74c3c', '#f39c12', '#9b59b6', '#95a5a6'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 扫码分类
    const scanWasteBtn = document.getElementById('scanWasteBtn');
    if (scanWasteBtn) {
        scanWasteBtn.addEventListener('click', function() {
            startWasteScanning();
        });
    }

    // 合规报告
    const complianceReportBtn = document.getElementById('complianceReportBtn');
    if (complianceReportBtn) {
        complianceReportBtn.addEventListener('click', function() {
            generateComplianceReport();
        });
    }

    // 环境监测
    const environmentMonitorBtn = document.getElementById('environmentMonitorBtn');
    if (environmentMonitorBtn) {
        environmentMonitorBtn.addEventListener('click', function() {
            showEnvironmentMonitor();
        });
    }

    // 启动扫描
    const startScanBtn = document.getElementById('startScanBtn');
    if (startScanBtn) {
        startScanBtn.addEventListener('click', function() {
            startScanning();
        });
    }

    // 新增分类
    const addCategoryBtn = document.getElementById('addCategoryBtn');
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function() {
            showCategoryForm();
        });
    }

    // 新建转运
    const newTransportBtn = document.getElementById('newTransportBtn');
    if (newTransportBtn) {
        newTransportBtn.addEventListener('click', function() {
            showTransportForm();
        });
    }

    // 新建审核
    const newAuditBtn = document.getElementById('newAuditBtn');
    if (newAuditBtn) {
        newAuditBtn.addEventListener('click', function() {
            showAuditForm();
        });
    }

    // 手动上报
    const manualAlertBtn = document.getElementById('manualAlertBtn');
    if (manualAlertBtn) {
        manualAlertBtn.addEventListener('click', function() {
            showAlertForm();
        });
    }

    // 生成环境报告
    const envReportBtn = document.getElementById('envReportBtn');
    if (envReportBtn) {
        envReportBtn.addEventListener('click', function() {
            generateEnvironmentReport();
        });
    }

    // 分类操作按钮
    const categoryActionBtns = document.querySelectorAll('.category-actions .btn');
    categoryActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const categoryCard = this.closest('.category-card');
            const categoryTitle = categoryCard.querySelector('h4').textContent;
            handleCategoryAction(action, categoryTitle);
        });
    });

    // 转运操作按钮
    const trackingActionBtns = document.querySelectorAll('.tracking-actions .btn');
    trackingActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const trackingItem = this.closest('.tracking-item');
            const batchNumber = trackingItem.querySelector('h4').textContent;
            handleTrackingAction(action, batchNumber);
        });
    });

    // 合规操作按钮
    const recordActionBtns = document.querySelectorAll('.record-actions .btn');
    recordActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const recordItem = this.closest('.record-item');
            const recordTitle = recordItem.querySelector('h4').textContent;
            handleRecordAction(action, recordTitle);
        });
    });

    // 预警操作按钮
    const alertActionBtns = document.querySelectorAll('.alert-actions-inline .btn');
    alertActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const alertItem = this.closest('.alert-item');
            const alertTitle = alertItem.querySelector('h4').textContent;
            handleAlertAction(action, alertTitle);
        });
    });
}

// 开始医废扫码
function startWasteScanning() {
    const modal = createModal('医废扫码分类', `
        <div class="scan-modal">
            <div class="scan-interface">
                <div class="scan-camera">
                    <div class="camera-frame">
                        <div class="scan-line"></div>
                        <div class="corner top-left"></div>
                        <div class="corner top-right"></div>
                        <div class="corner bottom-left"></div>
                        <div class="corner bottom-right"></div>
                    </div>
                    <p>请将医废二维码对准扫描框</p>
                </div>
                <div class="scan-result">
                    <h4>扫描结果</h4>
                    <div class="result-placeholder">
                        <i class="fas fa-qrcode"></i>
                        <p>等待扫描...</p>
                    </div>
                </div>
            </div>
            <div class="scan-actions">
                <button class="btn btn-secondary cancel-btn">取消</button>
                <button class="btn btn-primary" id="manualInputBtn">手动输入</button>
            </div>
        </div>
    `);

    // 模拟扫描过程
    setTimeout(() => {
        const resultDiv = modal.querySelector('.result-placeholder');
        resultDiv.innerHTML = `
            <div class="scan-success">
                <i class="fas fa-check-circle"></i>
                <h5>扫描成功</h5>
                <div class="waste-info">
                    <p><strong>编号:</strong> MW-2024-001258</p>
                    <p><strong>类型:</strong> 感染性废物</p>
                    <p><strong>来源:</strong> 外科病房</p>
                    <p><strong>重量:</strong> 3.2kg</p>
                </div>
                <button class="btn btn-primary">生成标签</button>
            </div>
        `;
    }, 2000);
}

// 生成合规报告
function generateComplianceReport() {
    Utils.showMessage('正在生成医废处置合规报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('合规报告生成成功，已下载到本地', 'success');
    }, 2000);
}

// 显示环境监测
function showEnvironmentMonitor() {
    Utils.showMessage('正在打开环境监测实时数据...', 'info');
    // 切换到环境监测标签页
    document.querySelector('[data-tab="environment"]').click();
}

// 启动扫描
function startScanning() {
    const scannerFrame = document.querySelector('.scanner-frame');
    const scanLine = scannerFrame.querySelector('.scan-line');
    
    // 添加扫描动画
    scanLine.style.animation = 'scan 2s linear infinite';
    
    Utils.showMessage('正在启动扫描设备...', 'info');
    
    // 模拟扫描结果
    setTimeout(() => {
        const resultDiv = document.querySelector('.classification-result');
        resultDiv.style.display = 'block';
        Utils.showMessage('扫描完成，已识别医废类型', 'success');
    }, 3000);
}

// 显示分类表单
function showCategoryForm() {
    const modal = createModal('新增医废分类', `
        <form class="category-form">
            <div class="form-row">
                <div class="form-group">
                    <label>分类名称 *</label>
                    <input type="text" name="categoryName" required placeholder="请输入分类名称">
                </div>
                <div class="form-group">
                    <label>危险等级 *</label>
                    <select name="dangerLevel" required>
                        <option value="">请选择危险等级</option>
                        <option value="high">高危</option>
                        <option value="medium">中危</option>
                        <option value="low">低危</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>处理方式 *</label>
                    <input type="text" name="treatmentMethod" required placeholder="请输入处理方式">
                </div>
                <div class="form-group">
                    <label>存储期限 *</label>
                    <input type="number" name="storageLimit" required placeholder="小时">
                </div>
            </div>
            <div class="form-group">
                <label>分类描述 *</label>
                <textarea name="description" required placeholder="请详细描述该分类的特征和要求"></textarea>
            </div>
            <div class="form-group">
                <label>识别标识</label>
                <input type="text" name="identifier" placeholder="用于自动识别的标识码">
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">添加分类</button>
            </div>
        </form>
    `);

    modal.querySelector('.category-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const name = e.target.categoryName.value;
        Utils.showMessage(`医废分类"${name}"已添加成功`, 'success');
        closeModal(modal);
    });
}

// 显示转运表单
function showTransportForm() {
    Utils.showMessage('正在打开转运任务创建页面...', 'info');
    // 这里可以添加实际的转运表单
}

// 显示审核表单
function showAuditForm() {
    Utils.showMessage('正在打开合规审核页面...', 'info');
    // 这里可以添加实际的审核表单
}

// 显示预警表单
function showAlertForm() {
    Utils.showMessage('正在打开手动预警上报页面...', 'info');
    // 这里可以添加实际的预警表单
}

// 生成环境报告
function generateEnvironmentReport() {
    Utils.showMessage('正在生成环境监测报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('环境监测报告生成成功', 'success');
    }, 2000);
}

// 处理分类操作
function handleCategoryAction(action, categoryTitle) {
    switch(action) {
        case '查看详情':
            Utils.showMessage(`正在查看"${categoryTitle}"详细信息...`, 'info');
            break;
        case '编辑规则':
            Utils.showMessage(`正在编辑"${categoryTitle}"分类规则...`, 'info');
            break;
    }
}

// 处理转运操作
function handleTrackingAction(action, batchNumber) {
    switch(action) {
        case '实时追踪':
            Utils.showMessage(`正在追踪${batchNumber}实时位置...`, 'info');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看${batchNumber}详细信息...`, 'info');
            break;
        case '查看报告':
            Utils.showMessage(`正在查看${batchNumber}处置报告...`, 'info');
            break;
        case '下载凭证':
            Utils.showMessage(`正在下载${batchNumber}处置凭证...`, 'info');
            break;
    }
}

// 处理合规操作
function handleRecordAction(action, recordTitle) {
    switch(action) {
        case '查看详情':
            Utils.showMessage(`正在查看"${recordTitle}"详细信息...`, 'info');
            break;
        case '下载证书':
            Utils.showMessage(`正在下载"${recordTitle}"合规证书...`, 'info');
            break;
        case '整改通知':
            Utils.showMessage(`正在发送"${recordTitle}"整改通知...`, 'info');
            break;
    }
}

// 处理预警操作
function handleAlertAction(action, alertTitle) {
    switch(action) {
        case '立即处理':
            Utils.showMessage(`正在处理"${alertTitle}"...`, 'info');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看"${alertTitle}"详细信息...`, 'info');
            break;
        case '联系科室':
            Utils.showMessage(`正在联系相关科室处理"${alertTitle}"...`, 'info');
            break;
        case '联系司机':
            Utils.showMessage(`正在联系转运司机...`, 'info');
            break;
        case '查看轨迹':
            Utils.showMessage(`正在查看转运轨迹...`, 'info');
            break;
        case '发送指令':
            Utils.showMessage(`正在发送纠正指令...`, 'info');
            break;
    }
}

// 初始化筛选功能
function initFilters() {
    // 分类筛选
    const categoryFilters = document.querySelectorAll('.category-filter');
    categoryFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterCategories(filterValue);
        });
    });

    // 状态筛选
    const statusFilters = document.querySelectorAll('.status-filter');
    statusFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByStatus(filterValue);
        });
    });

    // 合规筛选
    const complianceFilters = document.querySelectorAll('.compliance-filter');
    complianceFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterCompliance(filterValue);
        });
    });

    // 预警筛选
    const alertFilters = document.querySelectorAll('.alert-filter');
    alertFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterAlerts(filterValue);
        });
    });
}

// 筛选分类
function filterCategories(filterValue) {
    const categoryCards = document.querySelectorAll('.category-card');
    
    categoryCards.forEach(card => {
        if (filterValue === '全部类别') {
            card.style.display = 'block';
        } else {
            const categoryTitle = card.querySelector('h4').textContent;
            if (categoryTitle.includes(filterValue)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// 按状态筛选
function filterByStatus(filterValue) {
    const trackingItems = document.querySelectorAll('.tracking-item');
    
    trackingItems.forEach(item => {
        if (filterValue === '全部状态') {
            item.style.display = 'flex';
        } else {
            const status = item.querySelector('.tracking-status span').textContent;
            if (status.includes(filterValue)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 筛选合规记录
function filterCompliance(filterValue) {
    const recordItems = document.querySelectorAll('.record-item');
    
    recordItems.forEach(item => {
        if (filterValue === '全部记录') {
            item.style.display = 'flex';
        } else if (filterValue === '合规') {
            if (item.classList.contains('compliant')) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        } else if (filterValue === '违规') {
            if (item.classList.contains('non-compliant')) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 筛选预警
function filterAlerts(filterValue) {
    const alertItems = document.querySelectorAll('.alert-item');
    
    alertItems.forEach(item => {
        if (filterValue === '全部预警') {
            item.style.display = 'flex';
        } else if (filterValue === '紧急预警') {
            if (item.classList.contains('high-priority')) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        } else if (filterValue === '一般预警') {
            if (item.classList.contains('medium-priority')) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 初始化扫码功能
function initScanner() {
    // 这里可以集成实际的扫码库，如QuaggaJS或ZXing
    console.log('扫码功能已初始化');
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateWasteData, 30000);
}

// 更新医废数据
function updateWasteData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number && !isNaN(parseFloat(number.textContent))) {
            // 随机变化数据
            const currentValue = parseFloat(number.textContent);
            const change = (Math.random() - 0.5) * 2; // -1 到 1 的随机变化
            const newValue = Math.max(0, currentValue + change);
            
            if (Math.abs(newValue - currentValue) > 0.1) {
                number.textContent = newValue.toFixed(1);
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 标签页初始化函数
function initClassificationTab() {
    // 智能分类标签页初始化
}

function initTrackingTab() {
    // 转运追踪标签页初始化
}

function initComplianceTab() {
    // 合规监管标签页初始化
}

function initAlertsTab() {
    // 预警上报标签页初始化
}

function initEnvironmentTab() {
    // 环境监测标签页初始化
    // 重新初始化图表以确保正确显示
    setTimeout(() => {
        initCharts();
    }, 100);
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加基础样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 20px;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        .scan-modal {
            text-align: center;
        }
        .scan-interface {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }
        .camera-frame {
            width: 200px;
            height: 200px;
            border: 2px dashed #e74c3c;
            border-radius: 8px;
            margin: 0 auto 15px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
        }
        .scan-success {
            text-align: center;
        }
        .scan-success i {
            font-size: 48px;
            color: #27ae60;
            margin-bottom: 15px;
        }
        .waste-info {
            text-align: left;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .waste-info p {
            margin: 5px 0;
            font-size: 14px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-actions,
        .scan-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    const cancelBtn = modal.querySelector('.cancel-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
