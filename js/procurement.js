// 采购管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化搜索和筛选
    initSearchAndFilter();
    
    // 初始化表格交互
    initTableInteractions();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'intelligent':
                    refreshDemandAnalysis();
                    break;
                case 'suppliers':
                    loadSuppliers();
                    break;
                case 'bidding':
                    loadBiddings();
                    break;
                case 'contracts':
                    loadContracts();
                    break;
                case 'evaluation':
                    loadEvaluationData();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 需求预测图表
    const demandCtx = document.getElementById('demandChart');
    if (demandCtx) {
        new Chart(demandCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '实际需求',
                    data: [120, 150, 180, 140, 200, 160],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: '预测需求',
                    data: [130, 145, 175, 155, 195, 170],
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    borderDash: [5, 5]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 采购周期趋势图表
    const cycleCtx = document.getElementById('cycleChart');
    if (cycleCtx) {
        new Chart(cycleCtx, {
            type: 'bar',
            data: {
                labels: ['8月', '9月', '10月', '11月', '12月', '1月'],
                datasets: [{
                    label: '采购周期(天)',
                    data: [18, 16, 15, 14, 15, 13],
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: '#667eea',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 成本分析图表
    const costCtx = document.getElementById('costChart');
    if (costCtx) {
        new Chart(costCtx, {
            type: 'doughnut',
            data: {
                labels: ['办公设备', '办公耗材', '服务外包', '维修保养', '其他'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        '#667eea',
                        '#f39c12',
                        '#27ae60',
                        '#e74c3c',
                        '#95a5a6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建采购申请
    const newProcurementBtn = document.getElementById('newProcurementBtn');
    if (newProcurementBtn) {
        newProcurementBtn.addEventListener('click', function() {
            showProcurementForm();
        });
    }

    // 导出报告
    const exportReportBtn = document.getElementById('exportReportBtn');
    if (exportReportBtn) {
        exportReportBtn.addEventListener('click', function() {
            exportReport();
        });
    }

    // 新增供应商
    const addSupplierBtn = document.getElementById('addSupplierBtn');
    if (addSupplierBtn) {
        addSupplierBtn.addEventListener('click', function() {
            showSupplierForm();
        });
    }

    // 发起新招标
    const newBiddingBtn = document.getElementById('newBiddingBtn');
    if (newBiddingBtn) {
        newBiddingBtn.addEventListener('click', function() {
            showBiddingForm();
        });
    }

    // 合同模板按钮
    const templateBtns = document.querySelectorAll('.template-card .btn');
    templateBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const templateName = this.closest('.template-card').querySelector('h4').textContent;
            useContractTemplate(templateName);
        });
    });
}

// 显示采购申请表单
function showProcurementForm() {
    const modal = createModal('新建采购申请', `
        <form class="procurement-form">
            <div class="form-row">
                <div class="form-group">
                    <label>申请部门 *</label>
                    <input type="text" name="department" required placeholder="请输入申请部门">
                </div>
                <div class="form-group">
                    <label>申请人 *</label>
                    <input type="text" name="applicant" required placeholder="请输入申请人">
                </div>
            </div>
            <div class="form-group">
                <label>采购类别 *</label>
                <select name="category" required>
                    <option value="">请选择采购类别</option>
                    <option value="office">办公设备</option>
                    <option value="supplies">办公耗材</option>
                    <option value="service">服务外包</option>
                    <option value="maintenance">维修保养</option>
                </select>
            </div>
            <div class="form-group">
                <label>物品清单 *</label>
                <textarea name="items" required placeholder="请详细描述采购物品清单"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>预算金额 *</label>
                    <input type="number" name="budget" required placeholder="请输入预算金额">
                </div>
                <div class="form-group">
                    <label>期望交付时间 *</label>
                    <input type="date" name="deliveryDate" required>
                </div>
            </div>
            <div class="form-group">
                <label>采购理由 *</label>
                <textarea name="reason" required placeholder="请说明采购理由和用途"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交申请</button>
            </div>
        </form>
    `);

    modal.querySelector('.procurement-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('采购申请已提交，等待审批', 'success');
        closeModal(modal);
    });
}

// 显示供应商表单
function showSupplierForm() {
    const modal = createModal('新增供应商', `
        <form class="supplier-form">
            <div class="form-row">
                <div class="form-group">
                    <label>供应商名称 *</label>
                    <input type="text" name="name" required placeholder="请输入供应商名称">
                </div>
                <div class="form-group">
                    <label>联系人 *</label>
                    <input type="text" name="contact" required placeholder="请输入联系人">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>联系电话 *</label>
                    <input type="tel" name="phone" required placeholder="请输入联系电话">
                </div>
                <div class="form-group">
                    <label>邮箱地址</label>
                    <input type="email" name="email" placeholder="请输入邮箱地址">
                </div>
            </div>
            <div class="form-group">
                <label>公司地址</label>
                <input type="text" name="address" placeholder="请输入公司地址">
            </div>
            <div class="form-group">
                <label>业务范围 *</label>
                <textarea name="business" required placeholder="请描述主要业务范围"></textarea>
            </div>
            <div class="form-group">
                <label>资质证明</label>
                <input type="file" name="certificates" multiple accept=".pdf,.jpg,.png">
                <small>支持PDF、JPG、PNG格式，可多选</small>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交审核</button>
            </div>
        </form>
    `);

    modal.querySelector('.supplier-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('供应商信息已提交，等待审核', 'success');
        closeModal(modal);
    });
}

// 显示招标表单
function showBiddingForm() {
    const modal = createModal('发起新招标', `
        <form class="bidding-form">
            <div class="form-group">
                <label>招标项目名称 *</label>
                <input type="text" name="projectName" required placeholder="请输入招标项目名称">
            </div>
            <div class="form-group">
                <label>项目描述 *</label>
                <textarea name="description" required placeholder="请详细描述招标项目内容"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>预算金额 *</label>
                    <input type="number" name="budget" required placeholder="请输入预算金额">
                </div>
                <div class="form-group">
                    <label>招标截止时间 *</label>
                    <input type="datetime-local" name="deadline" required>
                </div>
            </div>
            <div class="form-group">
                <label>技术要求</label>
                <textarea name="requirements" placeholder="请描述技术要求和标准"></textarea>
            </div>
            <div class="form-group">
                <label>评标标准</label>
                <textarea name="criteria" placeholder="请描述评标标准和权重"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">发布招标</button>
            </div>
        </form>
    `);

    modal.querySelector('.bidding-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('招标信息已发布', 'success');
        closeModal(modal);
    });
}

// 使用合同模板
function useContractTemplate(templateName) {
    Utils.showMessage(`正在使用${templateName}...`, 'info');
    // 这里可以跳转到合同编辑页面或打开合同编辑器
}

// 导出报告
function exportReport() {
    Utils.showMessage('正在生成报告，请稍候...', 'info');
    // 模拟导出过程
    setTimeout(() => {
        Utils.showMessage('报告导出成功', 'success');
    }, 2000);
}

// 初始化搜索和筛选
function initSearchAndFilter() {
    const searchInput = document.querySelector('.search-input');
    const filterSelects = document.querySelectorAll('.filter-select');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const keyword = this.value.toLowerCase();
            filterSuppliers(keyword);
        });
    }

    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            const filterValue = this.value;
            applyFilter(filterValue);
        });
    });
}

// 筛选供应商
function filterSuppliers(keyword) {
    const supplierCards = document.querySelectorAll('.supplier-card');
    
    supplierCards.forEach(card => {
        const name = card.querySelector('.supplier-info h4').textContent.toLowerCase();
        const type = card.querySelector('.supplier-info p').textContent.toLowerCase();
        
        if (name.includes(keyword) || type.includes(keyword)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// 应用筛选
function applyFilter(filterValue) {
    // 根据筛选值应用相应的筛选逻辑
    console.log('应用筛选:', filterValue);
}

// 初始化表格交互
function initTableInteractions() {
    const tableRows = document.querySelectorAll('.contracts-table tr');
    
    tableRows.forEach(row => {
        row.addEventListener('click', function() {
            // 高亮选中行
            tableRows.forEach(r => r.classList.remove('selected'));
            this.classList.add('selected');
        });
    });
}

// 刷新需求分析
function refreshDemandAnalysis() {
    Utils.showMessage('正在刷新需求分析...', 'info');
    // 模拟数据刷新
}

// 加载供应商数据
function loadSuppliers() {
    // 模拟加载供应商数据
}

// 加载招标数据
function loadBiddings() {
    // 模拟加载招标数据
}

// 加载合同数据
function loadContracts() {
    // 模拟加载合同数据
}

// 加载评估数据
function loadEvaluationData() {
    // 模拟加载评估数据
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 20px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    modal.querySelector('.cancel-btn').addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
