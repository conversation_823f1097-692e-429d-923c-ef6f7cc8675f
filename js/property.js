// 物业管理系统页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化菜单切换
    initMenuToggle();
    
    // 初始化标签页
    initTabs();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
});

// 初始化菜单切换功能
function initMenuToggle() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
        });
    }
}

// 初始化标签页
function initTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
}

// 初始化图表
function initCharts() {
    // 收费统计趋势图表
    initRevenueChart();
    
    // 投诉类型分布图表
    initComplaintTypeChart();
}

// 收费统计趋势图表
function initRevenueChart() {
    const ctx = document.getElementById('revenueChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '物业费收入',
                data: [285000, 298000, 275000, 312000, 295000, 318000],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '水电费收入',
                data: [125000, 138000, 115000, 142000, 135000, 148000],
                borderColor: '#f093fb',
                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: '停车费收入',
                data: [45000, 48000, 42000, 52000, 49000, 55000],
                borderColor: '#ffeaa7',
                backgroundColor: 'rgba(255, 234, 167, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });
}

// 投诉类型分布图表
function initComplaintTypeChart() {
    const ctx = document.getElementById('complaintTypeChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['设备故障', '环境卫生', '噪音问题', '服务态度', '其他'],
            datasets: [{
                data: [35, 25, 20, 15, 5],
                backgroundColor: [
                    '#667eea',
                    '#f093fb',
                    '#ffeaa7',
                    '#fd79a8',
                    '#a29bfe'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}

// 初始化按钮事件
function initButtonEvents() {
    // 新增业主按钮
    const newOwnerBtn = document.getElementById('newOwnerBtn');
    if (newOwnerBtn) {
        newOwnerBtn.addEventListener('click', function() {
            showModal('新增业主', createNewOwnerForm());
        });
    }
    
    // 数据报表按钮
    const reportBtn = document.getElementById('reportBtn');
    if (reportBtn) {
        reportBtn.addEventListener('click', function() {
            generateReport();
        });
    }
    
    // 新增业主按钮（业主管理页面）
    const addOwnerBtn = document.getElementById('addOwnerBtn');
    if (addOwnerBtn) {
        addOwnerBtn.addEventListener('click', function() {
            showModal('新增业主', createNewOwnerForm());
        });
    }
    
    // 批量导入按钮
    const importOwnerBtn = document.getElementById('importOwnerBtn');
    if (importOwnerBtn) {
        importOwnerBtn.addEventListener('click', function() {
            showModal('批量导入业主', createImportOwnerForm());
        });
    }
    
    // 新建预订按钮
    const newBookingBtn = document.getElementById('newBookingBtn');
    if (newBookingBtn) {
        newBookingBtn.addEventListener('click', function() {
            showModal('新建设施预订', createNewBookingForm());
        });
    }
}

// 显示模态框
function showModal(title, content) {
    // 创建模态框HTML
    const modalHTML = `
        <div class="modal-overlay" id="modalOverlay">
            <div class="modal-container">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button class="btn btn-primary" onclick="submitModal()">确定</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 显示模态框
    document.getElementById('modalOverlay').style.display = 'flex';
}

// 关闭模态框
function closeModal() {
    const modal = document.getElementById('modalOverlay');
    if (modal) {
        modal.remove();
    }
}

// 提交模态框
function submitModal() {
    // 这里可以添加具体的提交逻辑
    alert('操作成功！');
    closeModal();
}

// 创建新增业主表单
function createNewOwnerForm() {
    return `
        <div class="form-container">
            <div class="form-row">
                <div class="form-group">
                    <label>业主姓名</label>
                    <input type="text" class="form-control" placeholder="请输入业主姓名">
                </div>
                <div class="form-group">
                    <label>联系电话</label>
                    <input type="tel" class="form-control" placeholder="请输入联系电话">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>房号</label>
                    <input type="text" class="form-control" placeholder="如：A-1205">
                </div>
                <div class="form-group">
                    <label>房屋类型</label>
                    <select class="form-control">
                        <option>自住</option>
                        <option>出租</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>身份证号</label>
                <input type="text" class="form-control" placeholder="请输入身份证号">
            </div>
            <div class="form-group">
                <label>入住时间</label>
                <input type="date" class="form-control">
            </div>
            <div class="form-group">
                <label>备注</label>
                <textarea class="form-control" rows="3" placeholder="请输入备注信息"></textarea>
            </div>
        </div>
    `;
}

// 创建批量导入表单
function createImportOwnerForm() {
    return `
        <div class="import-container">
            <div class="import-instructions">
                <h4>导入说明</h4>
                <ul>
                    <li>支持Excel文件格式（.xlsx, .xls）</li>
                    <li>文件大小不超过10MB</li>
                    <li>请按照模板格式填写数据</li>
                </ul>
                <button class="btn btn-secondary btn-sm">下载模板</button>
            </div>
            <div class="file-upload">
                <div class="upload-area">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>点击或拖拽文件到此处上传</p>
                    <input type="file" accept=".xlsx,.xls" style="display: none;">
                </div>
            </div>
        </div>
    `;
}

// 创建新建预订表单
function createNewBookingForm() {
    return `
        <div class="form-container">
            <div class="form-group">
                <label>预订设施</label>
                <select class="form-control">
                    <option>会议室A</option>
                    <option>会议室B</option>
                    <option>健身房</option>
                    <option>游泳池</option>
                    <option>活动室</option>
                </select>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>预订日期</label>
                    <input type="date" class="form-control">
                </div>
                <div class="form-group">
                    <label>预订时间</label>
                    <select class="form-control">
                        <option>08:00-10:00</option>
                        <option>10:00-12:00</option>
                        <option>14:00-16:00</option>
                        <option>16:00-18:00</option>
                        <option>18:00-20:00</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>预订人</label>
                <input type="text" class="form-control" placeholder="请输入预订人姓名">
            </div>
            <div class="form-group">
                <label>联系电话</label>
                <input type="tel" class="form-control" placeholder="请输入联系电话">
            </div>
            <div class="form-group">
                <label>预订用途</label>
                <textarea class="form-control" rows="3" placeholder="请描述预订用途"></textarea>
            </div>
        </div>
    `;
}

// 生成数据报表
function generateReport() {
    // 模拟生成报表
    const reportData = {
        generateTime: new Date().toLocaleString(),
        totalOwners: 1256,
        occupancyRate: '92.5%',
        paymentRate: '96.8%',
        pendingComplaints: 12
    };
    
    console.log('生成报表数据:', reportData);
    alert('数据报表生成成功！');
}
