// 安全生产管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化筛选功能
    initFilters();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'risk-control':
                    initRiskControlTab();
                    break;
                case 'emergency':
                    initEmergencyTab();
                    break;
                case 'training':
                    initTrainingTab();
                    break;
                case 'cases':
                    initCasesTab();
                    break;
                case 'behavior':
                    initBehaviorTab();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 安全行为趋势图
    const behaviorTrendCtx = document.getElementById('behaviorTrendChart');
    if (behaviorTrendCtx) {
        new Chart(behaviorTrendCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '安全评分',
                    data: [4.2, 4.3, 4.5, 4.4, 4.7, 4.8],
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '合规率',
                    data: [92, 94, 95, 93, 96, 96.5],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        min: 0,
                        max: 5,
                        grid: {
                            color: '#f0f0f0'
                        },
                        title: {
                            display: true,
                            text: '安全评分'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        min: 80,
                        max: 100,
                        grid: {
                            drawOnChartArea: false
                        },
                        title: {
                            display: true,
                            text: '合规率 (%)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 违规行为分布图
    const violationCtx = document.getElementById('violationChart');
    if (violationCtx) {
        new Chart(violationCtx, {
            type: 'doughnut',
            data: {
                labels: ['未佩戴防护用品', '违规操作', '区域违规', '其他'],
                datasets: [{
                    data: [35, 28, 22, 15],
                    backgroundColor: ['#e74c3c', '#f39c12', '#9b59b6', '#95a5a6'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 应急演练
    const emergencyDrillBtn = document.getElementById('emergencyDrillBtn');
    if (emergencyDrillBtn) {
        emergencyDrillBtn.addEventListener('click', function() {
            startEmergencyDrill();
        });
    }

    // 安全报告
    const safetyReportBtn = document.getElementById('safetyReportBtn');
    if (safetyReportBtn) {
        safetyReportBtn.addEventListener('click', function() {
            generateSafetyReport();
        });
    }

    // 风险评估
    const riskAssessmentBtn = document.getElementById('riskAssessmentBtn');
    if (riskAssessmentBtn) {
        riskAssessmentBtn.addEventListener('click', function() {
            showRiskAssessment();
        });
    }

    // 新增隐患
    const addRiskBtn = document.getElementById('addRiskBtn');
    if (addRiskBtn) {
        addRiskBtn.addEventListener('click', function() {
            showRiskForm();
        });
    }

    // 新建预案
    const newPlanBtn = document.getElementById('newPlanBtn');
    if (newPlanBtn) {
        newPlanBtn.addEventListener('click', function() {
            showPlanForm();
        });
    }

    // 新建培训
    const newTrainingBtn = document.getElementById('newTrainingBtn');
    if (newTrainingBtn) {
        newTrainingBtn.addEventListener('click', function() {
            showTrainingForm();
        });
    }

    // 添加案例
    const addCaseBtn = document.getElementById('addCaseBtn');
    if (addCaseBtn) {
        addCaseBtn.addEventListener('click', function() {
            showCaseForm();
        });
    }

    // 生成报告
    const generateReportBtn = document.getElementById('generateReportBtn');
    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', function() {
            generateBehaviorReport();
        });
    }

    // 风险操作按钮
    const riskActionBtns = document.querySelectorAll('.action-buttons .btn');
    riskActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const riskItem = this.closest('.risk-item');
            const riskTitle = riskItem.querySelector('h4').textContent;
            handleRiskAction(action, riskTitle);
        });
    });

    // 预案操作按钮
    const planActionBtns = document.querySelectorAll('.plan-actions .btn');
    planActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const planCard = this.closest('.plan-card');
            const planTitle = planCard.querySelector('h4').textContent;
            handlePlanAction(action, planTitle);
        });
    });

    // 案例操作按钮
    const caseActionBtns = document.querySelectorAll('.case-actions .btn');
    caseActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const caseItem = this.closest('.case-item');
            const caseTitle = caseItem.querySelector('h4').textContent;
            handleCaseAction(action, caseTitle);
        });
    });
}

// 开始应急演练
function startEmergencyDrill() {
    const modal = createModal('应急演练选择', `
        <div class="drill-selection">
            <h4>请选择演练类型</h4>
            <div class="drill-options">
                <div class="drill-option" data-type="fire">
                    <div class="option-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <h5>火灾应急演练</h5>
                    <p>模拟火灾场景，演练疏散和灭火流程</p>
                </div>
                <div class="drill-option" data-type="chemical">
                    <div class="option-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <h5>化学品泄漏演练</h5>
                    <p>模拟化学品泄漏，演练应急处理流程</p>
                </div>
                <div class="drill-option" data-type="medical">
                    <div class="option-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h5>医疗急救演练</h5>
                    <p>模拟工伤事故，演练急救处理流程</p>
                </div>
            </div>
            <div class="drill-actions">
                <button class="btn btn-secondary cancel-btn">取消</button>
                <button class="btn btn-primary start-drill-btn" disabled>开始演练</button>
            </div>
        </div>
    `);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .drill-selection {
            padding: 20px;
        }
        .drill-selection h4 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        .drill-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }
        .drill-option {
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .drill-option:hover {
            border-color: #667eea;
        }
        .drill-option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        .option-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin: 0 auto 15px;
        }
        .drill-option h5 {
            margin-bottom: 8px;
            color: #333;
        }
        .drill-option p {
            font-size: 13px;
            color: #666;
            margin: 0;
        }
        .drill-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
    `;
    document.head.appendChild(style);

    // 选择演练类型
    const drillOptions = modal.querySelectorAll('.drill-option');
    const startBtn = modal.querySelector('.start-drill-btn');
    let selectedType = null;

    drillOptions.forEach(option => {
        option.addEventListener('click', function() {
            drillOptions.forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedType = this.dataset.type;
            startBtn.disabled = false;
        });
    });

    startBtn.addEventListener('click', function() {
        if (selectedType) {
            Utils.showMessage(`正在启动${getTypeName(selectedType)}演练...`, 'info');
            closeModal(modal);
            // 这里可以添加实际的演练启动逻辑
        }
    });
}

// 获取类型名称
function getTypeName(type) {
    const typeNames = {
        'fire': '火灾应急',
        'chemical': '化学品泄漏',
        'medical': '医疗急救'
    };
    return typeNames[type] || type;
}

// 生成安全报告
function generateSafetyReport() {
    Utils.showMessage('正在生成安全生产报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('安全生产报告生成成功，已下载到本地', 'success');
    }, 2000);
}

// 显示风险评估
function showRiskAssessment() {
    Utils.showMessage('正在进行风险评估分析...', 'info');
    // 模拟风险评估过程
    setTimeout(() => {
        Utils.showMessage('风险评估完成，发现2个高风险项目', 'warning');
    }, 1500);
}

// 显示风险表单
function showRiskForm() {
    const modal = createModal('新增安全隐患', `
        <form class="risk-form">
            <div class="form-row">
                <div class="form-group">
                    <label>隐患标题 *</label>
                    <input type="text" name="riskTitle" required placeholder="请输入隐患标题">
                </div>
                <div class="form-group">
                    <label>风险等级 *</label>
                    <select name="riskLevel" required>
                        <option value="">请选择风险等级</option>
                        <option value="high">高风险</option>
                        <option value="medium">中风险</option>
                        <option value="low">低风险</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>所在区域 *</label>
                    <select name="riskArea" required>
                        <option value="">请选择区域</option>
                        <option value="production">生产车间</option>
                        <option value="warehouse">仓储区域</option>
                        <option value="office">办公区域</option>
                        <option value="public">公共区域</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>发现人员 *</label>
                    <input type="text" name="discoverer" required placeholder="请输入发现人员">
                </div>
            </div>
            <div class="form-group">
                <label>隐患描述 *</label>
                <textarea name="description" required placeholder="请详细描述安全隐患情况"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>严重性评分 (1-10)</label>
                    <input type="range" name="severity" min="1" max="10" value="5" class="range-input">
                    <span class="range-value">5</span>
                </div>
                <div class="form-group">
                    <label>可能性评分 (1-10)</label>
                    <input type="range" name="possibility" min="1" max="10" value="5" class="range-input">
                    <span class="range-value">5</span>
                </div>
            </div>
            <div class="form-group">
                <label>整改期限 *</label>
                <input type="date" name="deadline" required>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交隐患</button>
            </div>
        </form>
    `);

    // 范围输入更新
    const rangeInputs = modal.querySelectorAll('.range-input');
    rangeInputs.forEach(input => {
        const valueSpan = input.nextElementSibling;
        input.addEventListener('input', function() {
            valueSpan.textContent = this.value;
        });
    });

    modal.querySelector('.risk-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const title = e.target.riskTitle.value;
        Utils.showMessage(`安全隐患"${title}"已提交，等待审核`, 'success');
        closeModal(modal);
    });
}

// 显示预案表单
function showPlanForm() {
    Utils.showMessage('正在打开应急预案编辑器...', 'info');
    // 这里可以添加实际的预案表单
}

// 显示培训表单
function showTrainingForm() {
    Utils.showMessage('正在打开培训计划制定页面...', 'info');
    // 这里可以添加实际的培训表单
}

// 显示案例表单
function showCaseForm() {
    Utils.showMessage('正在打开事故案例录入页面...', 'info');
    // 这里可以添加实际的案例表单
}

// 生成行为报告
function generateBehaviorReport() {
    Utils.showMessage('正在生成员工安全行为分析报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('安全行为分析报告生成成功', 'success');
    }, 2000);
}

// 处理风险操作
function handleRiskAction(action, riskTitle) {
    switch(action) {
        case '立即整改':
            Utils.showMessage(`正在安排"${riskTitle}"立即整改...`, 'info');
            break;
        case '安排整改':
            Utils.showMessage(`已安排"${riskTitle}"整改计划`, 'success');
            break;
        case '计划整改':
            Utils.showMessage(`已将"${riskTitle}"加入整改计划`, 'success');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看"${riskTitle}"详细信息...`, 'info');
            break;
    }
}

// 处理预案操作
function handlePlanAction(action, planTitle) {
    switch(action) {
        case '开始演练':
            Utils.showMessage(`正在启动"${planTitle}"演练...`, 'info');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看"${planTitle}"详细信息...`, 'info');
            break;
        case '编辑预案':
            Utils.showMessage(`正在编辑"${planTitle}"...`, 'info');
            break;
    }
}

// 处理案例操作
function handleCaseAction(action, caseTitle) {
    switch(action) {
        case '详细学习':
            Utils.showMessage(`正在打开"${caseTitle}"学习页面...`, 'info');
            break;
        case '分享案例':
            Utils.showMessage(`正在分享"${caseTitle}"...`, 'info');
            break;
    }
}

// 初始化筛选功能
function initFilters() {
    // 风险筛选
    const riskFilters = document.querySelectorAll('.risk-filter');
    riskFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterRisks(filterValue);
        });
    });

    // 区域筛选
    const areaFilters = document.querySelectorAll('.area-filter');
    areaFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByArea(filterValue);
        });
    });

    // 员工筛选
    const employeeFilters = document.querySelectorAll('.employee-filter');
    employeeFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterEmployees(filterValue);
        });
    });
}

// 筛选风险
function filterRisks(filterValue) {
    const riskItems = document.querySelectorAll('.risk-item');
    
    riskItems.forEach(item => {
        if (filterValue === '全部风险') {
            item.style.display = 'flex';
        } else {
            const riskLevel = item.querySelector('.risk-level').textContent;
            if (riskLevel.includes(filterValue.replace('风险', ''))) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 按区域筛选
function filterByArea(filterValue) {
    const riskItems = document.querySelectorAll('.risk-item');
    
    riskItems.forEach(item => {
        if (filterValue === '全部区域') {
            item.style.display = 'flex';
        } else {
            const area = item.querySelector('.risk-area').textContent;
            if (area.includes(filterValue)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 筛选员工
function filterEmployees(filterValue) {
    const profileItems = document.querySelectorAll('.profile-item');
    
    profileItems.forEach(item => {
        if (filterValue === '全部员工') {
            item.style.display = 'flex';
        } else if (filterValue === '高风险员工') {
            if (item.classList.contains('warning')) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        } else {
            // 其他筛选条件可以根据需要添加
            item.style.display = 'flex';
        }
    });
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateSafetyData, 30000);
}

// 更新安全数据
function updateSafetyData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number && !isNaN(parseInt(number.textContent))) {
            // 随机变化数据
            const currentValue = parseInt(number.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
            const newValue = Math.max(0, currentValue + change);
            
            if (newValue !== currentValue) {
                number.textContent = newValue;
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 标签页初始化函数
function initRiskControlTab() {
    // 风险管控标签页初始化
}

function initEmergencyTab() {
    // 应急预案标签页初始化
}

function initTrainingTab() {
    // 安全培训标签页初始化
}

function initCasesTab() {
    // 事故案例标签页初始化
}

function initBehaviorTab() {
    // 行为画像标签页初始化
    // 重新初始化图表以确保正确显示
    setTimeout(() => {
        initCharts();
    }, 100);
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加基础样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 0;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .range-input {
            width: calc(100% - 40px);
        }
        .range-value {
            display: inline-block;
            width: 30px;
            text-align: center;
            font-weight: 600;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    const cancelBtn = modal.querySelector('.cancel-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
