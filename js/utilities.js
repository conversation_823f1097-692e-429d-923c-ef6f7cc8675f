// 水电气管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化筛选功能
    initFilters();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'monitoring':
                    initMonitoringTab();
                    break;
                case 'meters':
                    initMetersTab();
                    break;
                case 'alerts':
                    initAlertsTab();
                    break;
                case 'optimization':
                    initOptimizationTab();
                    break;
                case 'carbon':
                    initCarbonTab();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 能耗趋势图
    const energyTrendCtx = document.getElementById('energyTrendChart');
    if (energyTrendCtx) {
        new Chart(energyTrendCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                datasets: [{
                    label: '电力消耗 (kWh)',
                    data: [11200, 10800, 12100, 11900, 12800, 13200, 14100, 13800, 12900, 12200, 11800, 12456],
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    tension: 0.4
                }, {
                    label: '用水量 (m³)',
                    data: [2800, 2600, 2900, 3100, 3300, 3500, 3800, 3600, 3400, 3200, 3000, 3280],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4
                }, {
                    label: '燃气消耗 (m³)',
                    data: [1600, 1500, 1700, 1800, 1900, 2000, 2100, 2000, 1900, 1800, 1700, 1890],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 能耗分布饼图
    const energyDistributionCtx = document.getElementById('energyDistributionChart');
    if (energyDistributionCtx) {
        new Chart(energyDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['电力', '用水', '燃气'],
                datasets: [{
                    data: [65, 25, 10],
                    backgroundColor: ['#f39c12', '#3498db', '#e74c3c'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // 碳排放构成图
    const carbonCompositionCtx = document.getElementById('carbonCompositionChart');
    if (carbonCompositionCtx) {
        new Chart(carbonCompositionCtx, {
            type: 'pie',
            data: {
                labels: ['电力排放', '燃气排放', '用水排放'],
                datasets: [{
                    data: [82.6, 14.0, 3.4],
                    backgroundColor: ['#27ae60', '#f39c12', '#3498db'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // 碳排放趋势图
    const carbonTrendCtx = document.getElementById('carbonTrendChart');
    if (carbonTrendCtx) {
        new Chart(carbonTrendCtx, {
            type: 'bar',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '碳排放量 (tCO₂)',
                    data: [9.2, 8.8, 9.5, 9.1, 9.8, 8.6],
                    backgroundColor: 'rgba(39, 174, 96, 0.8)',
                    borderColor: '#27ae60',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 能耗报告
    const energyReportBtn = document.getElementById('energyReportBtn');
    if (energyReportBtn) {
        energyReportBtn.addEventListener('click', function() {
            generateEnergyReport();
        });
    }

    // 碳排放报告
    const carbonReportBtn = document.getElementById('carbonReportBtn');
    if (carbonReportBtn) {
        carbonReportBtn.addEventListener('click', function() {
            generateCarbonReport();
        });
    }

    // 节能优化
    const optimizationBtn = document.getElementById('optimizationBtn');
    if (optimizationBtn) {
        optimizationBtn.addEventListener('click', function() {
            showOptimizationPlan();
        });
    }

    // 生成优化方案
    const generatePlanBtn = document.getElementById('generatePlanBtn');
    if (generatePlanBtn) {
        generatePlanBtn.addEventListener('click', function() {
            generateOptimizationPlan();
        });
    }

    // 表计操作按钮
    const meterActionBtns = document.querySelectorAll('.meter-actions .btn');
    meterActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const meterCard = this.closest('.meter-card');
            const meterName = meterCard.querySelector('h4').textContent;
            handleMeterAction(action, meterName);
        });
    });

    // 预警处理按钮
    const alertActionBtns = document.querySelectorAll('.alert-actions .btn');
    alertActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const alertItem = this.closest('.alert-item');
            const alertTitle = alertItem.querySelector('h4').textContent;
            handleAlertAction(action, alertTitle);
        });
    });

    // 优化方案按钮
    const recActionBtns = document.querySelectorAll('.rec-actions .btn');
    recActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const recItem = this.closest('.recommendation-item');
            const recTitle = recItem.querySelector('h4').textContent;
            handleRecommendationAction(action, recTitle);
        });
    });
}

// 生成能耗报告
function generateEnergyReport() {
    Utils.showMessage('正在生成能耗报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('能耗报告生成成功，已下载到本地', 'success');
    }, 2000);
}

// 生成碳排放报告
function generateCarbonReport() {
    Utils.showMessage('正在生成碳排放报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('碳排放报告生成成功，已下载到本地', 'success');
    }, 2000);
}

// 显示优化方案
function showOptimizationPlan() {
    const modal = createModal('节能优化方案', `
        <div class="optimization-plan">
            <div class="plan-overview">
                <h4>综合节能方案</h4>
                <div class="plan-stats">
                    <div class="plan-stat">
                        <span class="stat-label">预计节能率</span>
                        <span class="stat-value">15.8%</span>
                    </div>
                    <div class="plan-stat">
                        <span class="stat-label">年节约费用</span>
                        <span class="stat-value">¥45,600</span>
                    </div>
                    <div class="plan-stat">
                        <span class="stat-label">投资回收期</span>
                        <span class="stat-value">2.1年</span>
                    </div>
                </div>
            </div>
            <div class="plan-items">
                <div class="plan-item">
                    <h5>LED照明系统改造</h5>
                    <p>更换传统荧光灯为LED灯具，预计节能60%</p>
                    <div class="item-details">
                        <span>投资: ¥28,000</span>
                        <span>年节约: ¥15,600</span>
                    </div>
                </div>
                <div class="plan-item">
                    <h5>空调系统优化</h5>
                    <p>安装变频控制器，优化运行策略，预计节能25%</p>
                    <div class="item-details">
                        <span>投资: ¥45,000</span>
                        <span>年节约: ¥18,200</span>
                    </div>
                </div>
                <div class="plan-item">
                    <h5>智能用水管理</h5>
                    <p>安装智能水表和节水设备，预计节水20%</p>
                    <div class="item-details">
                        <span>投资: ¥15,000</span>
                        <span>年节约: ¥11,800</span>
                    </div>
                </div>
            </div>
            <div class="plan-actions">
                <button class="btn btn-primary">制定实施计划</button>
                <button class="btn btn-secondary">详细分析报告</button>
            </div>
        </div>
    `);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .optimization-plan {
            padding: 20px;
        }
        .plan-overview {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .plan-overview h4 {
            margin-bottom: 15px;
            color: #333;
        }
        .plan-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
        .plan-stat {
            text-align: center;
        }
        .stat-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: 700;
            color: #667eea;
        }
        .plan-items {
            margin-bottom: 25px;
        }
        .plan-item {
            padding: 15px;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            margin-bottom: 10px;
        }
        .plan-item h5 {
            margin-bottom: 8px;
            color: #333;
        }
        .plan-item p {
            margin-bottom: 10px;
            color: #666;
            font-size: 14px;
        }
        .item-details {
            display: flex;
            gap: 20px;
            font-size: 12px;
            color: #999;
        }
        .plan-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
    `;
    document.head.appendChild(style);
}

// 生成优化方案
function generateOptimizationPlan() {
    Utils.showMessage('正在分析能耗数据，生成优化方案...', 'info');
    // 模拟方案生成过程
    setTimeout(() => {
        Utils.showMessage('优化方案生成完成，发现3个节能机会', 'success');
    }, 3000);
}

// 处理表计操作
function handleMeterAction(action, meterName) {
    switch(action) {
        case '查看详情':
            Utils.showMessage(`正在查看"${meterName}"详细信息...`, 'info');
            break;
        case '历史数据':
            Utils.showMessage(`正在加载"${meterName}"历史数据...`, 'info');
            break;
        case '检查连接':
            Utils.showMessage(`正在检查"${meterName}"连接状态...`, 'info');
            // 模拟检查过程
            setTimeout(() => {
                Utils.showMessage('连接检查完成，已尝试重新连接', 'success');
            }, 2000);
            break;
    }
}

// 处理预警操作
function handleAlertAction(action, alertTitle) {
    switch(action) {
        case '立即处理':
            Utils.showMessage(`正在处理预警: ${alertTitle}`, 'info');
            // 模拟处理过程
            setTimeout(() => {
                Utils.showMessage('预警处理完成，已派遣技术人员', 'success');
            }, 1500);
            break;
        case '关注处理':
            Utils.showMessage(`已将预警加入关注列表: ${alertTitle}`, 'success');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看预警详情: ${alertTitle}`, 'info');
            break;
    }
}

// 处理优化建议操作
function handleRecommendationAction(action, recTitle) {
    switch(action) {
        case '制定计划':
            Utils.showMessage(`正在为"${recTitle}"制定实施计划...`, 'info');
            break;
        case '详细分析':
            Utils.showMessage(`正在生成"${recTitle}"详细分析报告...`, 'info');
            break;
    }
}

// 初始化筛选功能
function initFilters() {
    // 时间范围筛选
    const timeRangeSelects = document.querySelectorAll('.time-range-select');
    timeRangeSelects.forEach(select => {
        select.addEventListener('change', function() {
            const timeRange = this.value;
            updateChartData(timeRange);
        });
    });

    // 能源类型筛选
    const energyTypeSelects = document.querySelectorAll('.energy-type-select');
    energyTypeSelects.forEach(select => {
        select.addEventListener('change', function() {
            const energyType = this.value;
            filterEnergyData(energyType);
        });
    });

    // 表计筛选
    const filterSelects = document.querySelectorAll('.filter-select');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            const filterType = this.value;
            filterMeters(filterType);
        });
    });

    // 预警筛选
    const alertFilters = document.querySelectorAll('.alert-filter');
    alertFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const alertLevel = this.value;
            filterAlerts(alertLevel);
        });
    });
}

// 更新图表数据
function updateChartData(timeRange) {
    Utils.showMessage(`正在加载${timeRange}数据...`, 'info');
    // 这里可以添加实际的数据更新逻辑
}

// 筛选能源数据
function filterEnergyData(energyType) {
    Utils.showMessage(`正在筛选${energyType}数据...`, 'info');
    // 这里可以添加实际的筛选逻辑
}

// 筛选表计
function filterMeters(filterType) {
    const meterCards = document.querySelectorAll('.meter-card');
    
    meterCards.forEach(card => {
        if (filterType === '全部表计') {
            card.style.display = 'block';
        } else {
            const meterType = card.querySelector('.meter-type span').textContent;
            if (meterType.includes(filterType.replace('表', ''))) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// 筛选预警
function filterAlerts(alertLevel) {
    const alertItems = document.querySelectorAll('.alert-item');
    
    alertItems.forEach(item => {
        if (alertLevel === '全部预警') {
            item.style.display = 'flex';
        } else {
            const itemLevel = item.querySelector('.alert-level').textContent;
            if (itemLevel.includes(alertLevel.replace('风险', ''))) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateUtilitiesData, 30000);
}

// 更新水电气数据
function updateUtilitiesData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number) {
            const currentText = number.textContent;
            const currentValue = parseFloat(currentText.replace(/[^\d.]/g, ''));
            
            if (!isNaN(currentValue)) {
                // 随机变化数据 (-2% 到 +2%)
                const changePercent = (Math.random() - 0.5) * 0.04;
                const newValue = currentValue * (1 + changePercent);
                
                // 更新显示
                const unit = currentText.match(/[a-zA-Z₂³]+/);
                const unitText = unit ? unit[0] : '';
                
                if (currentText.includes(',')) {
                    number.innerHTML = `${Math.round(newValue).toLocaleString()}<span class="unit">${unitText}</span>`;
                } else {
                    number.innerHTML = `${newValue.toFixed(1)}<span class="unit">${unitText}</span>`;
                }
                
                // 添加更新动画
                number.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 标签页初始化函数
function initMonitoringTab() {
    // 能耗监测标签页初始化
}

function initMetersTab() {
    // 智能表计标签页初始化
}

function initAlertsTab() {
    // 异常预警标签页初始化
}

function initOptimizationTab() {
    // 节能优化标签页初始化
}

function initCarbonTab() {
    // 碳排放核算标签页初始化
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加基础样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 0;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}
