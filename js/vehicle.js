// 用车管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化筛选功能
    initFilters();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'dispatch':
                    initDispatchTab();
                    break;
                case 'safety':
                    initSafetyTab();
                    break;
                case 'cost':
                    initCostTab();
                    break;
                case 'health':
                    initHealthTab();
                    break;
                case 'electric':
                    initElectricTab();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 费用趋势图
    const costTrendCtx = document.getElementById('costTrendChart');
    if (costTrendCtx) {
        new Chart(costTrendCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '燃油费',
                    data: [18560, 17890, 19200, 18340, 17650, 18560],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '维修费',
                    data: [5240, 4890, 6100, 5560, 4920, 5240],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: '其他费用',
                    data: [2570, 2340, 2890, 2650, 2420, 2570],
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        },
                        title: {
                            display: true,
                            text: '费用 (元)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 费用构成图
    const costStructureCtx = document.getElementById('costStructureChart');
    if (costStructureCtx) {
        new Chart(costStructureCtx, {
            type: 'doughnut',
            data: {
                labels: ['燃油费', '维修费', '过路费', '停车费', '其他'],
                datasets: [{
                    data: [18560, 5240, 1890, 680, 1200],
                    backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建用车
    const newBookingBtn = document.getElementById('newBookingBtn');
    if (newBookingBtn) {
        newBookingBtn.addEventListener('click', function() {
            showBookingForm();
        });
    }

    // 调度优化
    const dispatchOptimizeBtn = document.getElementById('dispatchOptimizeBtn');
    if (dispatchOptimizeBtn) {
        dispatchOptimizeBtn.addEventListener('click', function() {
            optimizeDispatch();
        });
    }

    // 车辆报告
    const vehicleReportBtn = document.getElementById('vehicleReportBtn');
    if (vehicleReportBtn) {
        vehicleReportBtn.addEventListener('click', function() {
            generateVehicleReport();
        });
    }

    // 智能优化
    const optimizeBtn = document.getElementById('optimizeBtn');
    if (optimizeBtn) {
        optimizeBtn.addEventListener('click', function() {
            runAIOptimization();
        });
    }

    // 安全报告
    const safetyReportBtn = document.getElementById('safetyReportBtn');
    if (safetyReportBtn) {
        safetyReportBtn.addEventListener('click', function() {
            generateSafetyReport();
        });
    }

    // 生成费用报表
    const generateCostReportBtn = document.getElementById('generateCostReportBtn');
    if (generateCostReportBtn) {
        generateCostReportBtn.addEventListener('click', function() {
            generateCostReport();
        });
    }

    // 添加车辆
    const addVehicleBtn = document.getElementById('addVehicleBtn');
    if (addVehicleBtn) {
        addVehicleBtn.addEventListener('click', function() {
            showVehicleForm();
        });
    }

    // 查找充电站
    const findStationBtn = document.getElementById('findStationBtn');
    if (findStationBtn) {
        findStationBtn.addEventListener('click', function() {
            findChargingStations();
        });
    }

    // 申请操作按钮
    const requestActionBtns = document.querySelectorAll('.request-actions .btn');
    requestActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const requestItem = this.closest('.request-item');
            const requestTitle = requestItem.querySelector('h4').textContent;
            handleRequestAction(action, requestTitle);
        });
    });

    // 监控操作按钮
    const monitorActionBtns = document.querySelectorAll('.monitor-actions .btn');
    monitorActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const monitorItem = this.closest('.vehicle-monitor');
            const vehicleTitle = monitorItem.querySelector('h4').textContent;
            handleMonitorAction(action, vehicleTitle);
        });
    });

    // 预警操作按钮
    const alertActionBtns = document.querySelectorAll('.alert-actions .btn');
    alertActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const alertItem = this.closest('.alert-item');
            const alertTitle = alertItem.querySelector('h5').textContent;
            handleAlertAction(action, alertTitle);
        });
    });

    // 车辆操作按钮
    const vehicleActionBtns = document.querySelectorAll('.vehicle-actions .btn');
    vehicleActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const vehicleCard = this.closest('.vehicle-card');
            const vehicleTitle = vehicleCard.querySelector('h4').textContent;
            handleVehicleAction(action, vehicleTitle);
        });
    });

    // 充电站操作按钮
    const stationActionBtns = document.querySelectorAll('.station-actions .btn');
    stationActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const stationItem = this.closest('.station-item');
            const stationTitle = stationItem.querySelector('h4').textContent;
            handleStationAction(action, stationTitle);
        });
    });

    // 新能源车操作按钮
    const evActionBtns = document.querySelectorAll('.ev-actions .btn');
    evActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const evCard = this.closest('.ev-card');
            const evTitle = evCard.querySelector('h4').textContent;
            handleEVAction(action, evTitle);
        });
    });
}

// 显示用车申请表单
function showBookingForm() {
    const modal = createModal('新建用车申请', `
        <form class="booking-form">
            <div class="form-row">
                <div class="form-group">
                    <label>申请人 *</label>
                    <input type="text" name="applicant" required placeholder="请输入申请人姓名">
                </div>
                <div class="form-group">
                    <label>联系电话 *</label>
                    <input type="tel" name="phone" required placeholder="请输入联系电话">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>用车时间 *</label>
                    <input type="datetime-local" name="startTime" required>
                </div>
                <div class="form-group">
                    <label>预计结束时间 *</label>
                    <input type="datetime-local" name="endTime" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>出发地 *</label>
                    <input type="text" name="departure" required placeholder="请输入出发地">
                </div>
                <div class="form-group">
                    <label>目的地 *</label>
                    <input type="text" name="destination" required placeholder="请输入目的地">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>车辆类型 *</label>
                    <select name="vehicleType" required>
                        <option value="">请选择车辆类型</option>
                        <option value="sedan">轿车</option>
                        <option value="suv">SUV</option>
                        <option value="van">商务车</option>
                        <option value="truck">货车</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>优先级 *</label>
                    <select name="priority" required>
                        <option value="">请选择优先级</option>
                        <option value="urgent">紧急</option>
                        <option value="normal">普通</option>
                        <option value="low">低优先级</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>用车事由 *</label>
                <textarea name="reason" required placeholder="请详细说明用车事由"></textarea>
            </div>
            <div class="form-group">
                <label>乘车人数</label>
                <input type="number" name="passengers" min="1" max="20" placeholder="请输入乘车人数">
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交申请</button>
            </div>
        </form>
    `);

    modal.querySelector('.booking-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const applicant = e.target.applicant.value;
        Utils.showMessage(`用车申请已提交，申请人: ${applicant}`, 'success');
        closeModal(modal);
    });
}

// 调度优化
function optimizeDispatch() {
    Utils.showMessage('正在运行智能调度优化算法...', 'info');
    // 模拟优化过程
    setTimeout(() => {
        Utils.showMessage('调度优化完成，预计节省30%的运营成本', 'success');
    }, 2000);
}

// 生成车辆报告
function generateVehicleReport() {
    Utils.showMessage('正在生成车辆管理报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('车辆管理报告生成成功，已下载到本地', 'success');
    }, 2000);
}

// 运行AI优化
function runAIOptimization() {
    Utils.showMessage('正在运行AI智能优化...', 'info');
    // 模拟AI优化过程
    setTimeout(() => {
        Utils.showMessage('AI优化完成，已更新推荐方案', 'success');
        // 更新推荐方案显示
        updateRecommendations();
    }, 3000);
}

// 更新推荐方案
function updateRecommendations() {
    const recommendations = document.querySelectorAll('.ai-recommendation');
    recommendations.forEach(rec => {
        const scoreElement = rec.querySelector('.score');
        if (scoreElement) {
            const currentScore = parseInt(scoreElement.textContent);
            const newScore = Math.min(100, currentScore + Math.floor(Math.random() * 10));
            scoreElement.textContent = newScore + '分';
            
            // 更新评分颜色
            scoreElement.className = 'score ' + (newScore >= 90 ? 'high' : 'medium');
        }
    });
}

// 生成安全报告
function generateSafetyReport() {
    Utils.showMessage('正在生成行车安全报告...', 'info');
    // 模拟报告生成过程
    setTimeout(() => {
        Utils.showMessage('行车安全报告生成成功', 'success');
    }, 2000);
}

// 生成费用报表
function generateCostReport() {
    Utils.showMessage('正在生成费用分摊报表...', 'info');
    // 模拟报表生成过程
    setTimeout(() => {
        Utils.showMessage('费用分摊报表生成成功，已导出Excel文件', 'success');
    }, 2000);
}

// 显示车辆表单
function showVehicleForm() {
    Utils.showMessage('正在打开车辆信息录入页面...', 'info');
    // 这里可以添加实际的车辆表单
}

// 查找充电站
function findChargingStations() {
    Utils.showMessage('正在搜索附近充电站...', 'info');
    // 模拟搜索过程
    setTimeout(() => {
        Utils.showMessage('已找到8个可用充电站', 'success');
    }, 1500);
}

// 处理申请操作
function handleRequestAction(action, requestTitle) {
    switch(action) {
        case '立即分配':
            Utils.showMessage(`正在为"${requestTitle}"立即分配车辆...`, 'info');
            break;
        case '分配车辆':
            Utils.showMessage(`正在为"${requestTitle}"分配车辆...`, 'info');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看"${requestTitle}"详细信息...`, 'info');
            break;
        case '联系申请人':
            Utils.showMessage(`正在联系"${requestTitle}"申请人...`, 'info');
            break;
        case '修改申请':
            Utils.showMessage(`正在修改"${requestTitle}"申请...`, 'info');
            break;
    }
}

// 处理监控操作
function handleMonitorAction(action, vehicleTitle) {
    switch(action) {
        case '发送提醒':
            Utils.showMessage(`正在向${vehicleTitle}司机发送安全提醒...`, 'info');
            break;
        case '联系司机':
            Utils.showMessage(`正在联系${vehicleTitle}司机...`, 'info');
            break;
    }
}

// 处理预警操作
function handleAlertAction(action, alertTitle) {
    switch(action) {
        case '立即处理':
            Utils.showMessage(`正在处理"${alertTitle}"...`, 'info');
            break;
        case '发送提醒':
            Utils.showMessage(`正在发送"${alertTitle}"提醒...`, 'info');
            break;
    }
}

// 处理车辆操作
function handleVehicleAction(action, vehicleTitle) {
    switch(action) {
        case '查看档案':
            Utils.showMessage(`正在查看${vehicleTitle}详细档案...`, 'info');
            break;
        case '维修记录':
            Utils.showMessage(`正在查看${vehicleTitle}维修记录...`, 'info');
            break;
        case '安排保养':
            Utils.showMessage(`正在为${vehicleTitle}安排保养...`, 'info');
            break;
        case '联系维修厂':
            Utils.showMessage(`正在联系${vehicleTitle}维修厂...`, 'info');
            break;
        case '维修进度':
            Utils.showMessage(`正在查看${vehicleTitle}维修进度...`, 'info');
            break;
    }
}

// 处理充电站操作
function handleStationAction(action, stationTitle) {
    switch(action) {
        case '导航前往':
            Utils.showMessage(`正在导航前往${stationTitle}...`, 'info');
            break;
        case '预约充电':
            Utils.showMessage(`正在预约${stationTitle}充电...`, 'info');
            break;
    }
}

// 处理新能源车操作
function handleEVAction(action, evTitle) {
    switch(action) {
        case '实时追踪':
            Utils.showMessage(`正在追踪${evTitle}实时位置...`, 'info');
            break;
        case '安排充电':
            Utils.showMessage(`正在为${evTitle}安排充电...`, 'info');
            break;
    }
}

// 初始化筛选功能
function initFilters() {
    // 优先级筛选
    const priorityFilters = document.querySelectorAll('.priority-filter');
    priorityFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByPriority(filterValue);
        });
    });

    // 状态筛选
    const statusFilters = document.querySelectorAll('.status-filter');
    statusFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByStatus(filterValue);
        });
    });

    // 车辆筛选
    const vehicleFilters = document.querySelectorAll('.vehicle-filter');
    vehicleFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterVehicles(filterValue);
        });
    });

    // 健康状态筛选
    const healthFilters = document.querySelectorAll('.health-filter');
    healthFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            const filterValue = this.value;
            filterByHealth(filterValue);
        });
    });
}

// 按优先级筛选
function filterByPriority(filterValue) {
    const requestItems = document.querySelectorAll('.request-item');
    
    requestItems.forEach(item => {
        if (filterValue === '全部优先级') {
            item.style.display = 'flex';
        } else {
            const priority = item.querySelector('.priority-badge').textContent;
            if (priority.includes(filterValue)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 按状态筛选
function filterByStatus(filterValue) {
    const items = document.querySelectorAll('.request-item, .tracking-item');
    
    items.forEach(item => {
        if (filterValue === '全部状态') {
            item.style.display = 'flex';
        } else {
            // 根据具体的状态筛选逻辑
            item.style.display = 'flex';
        }
    });
}

// 筛选车辆
function filterVehicles(filterValue) {
    const monitorItems = document.querySelectorAll('.vehicle-monitor');
    
    monitorItems.forEach(item => {
        if (filterValue === '全部车辆') {
            item.style.display = 'block';
        } else if (filterValue === '在线车辆') {
            if (item.classList.contains('normal')) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        } else if (filterValue === '异常车辆') {
            if (item.classList.contains('warning')) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

// 按健康状态筛选
function filterByHealth(filterValue) {
    const vehicleCards = document.querySelectorAll('.vehicle-card');
    
    vehicleCards.forEach(card => {
        if (filterValue === '全部状态') {
            card.style.display = 'block';
        } else if (filterValue === '健康') {
            if (card.classList.contains('healthy')) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        } else if (filterValue === '需保养') {
            if (card.classList.contains('maintenance')) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        } else if (filterValue === '维修中') {
            if (card.classList.contains('repair')) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateVehicleData, 30000);
}

// 更新车辆数据
function updateVehicleData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number && !isNaN(parseInt(number.textContent))) {
            // 随机变化数据
            const currentValue = parseInt(number.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
            const newValue = Math.max(0, currentValue + change);
            
            if (newValue !== currentValue) {
                number.textContent = newValue;
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 标签页初始化函数
function initDispatchTab() {
    // 智能调度标签页初始化
}

function initSafetyTab() {
    // 安全监测标签页初始化
}

function initCostTab() {
    // 费用核算标签页初始化
    // 重新初始化图表以确保正确显示
    setTimeout(() => {
        initCharts();
    }, 100);
}

function initHealthTab() {
    // 健康档案标签页初始化
}

function initElectricTab() {
    // 新能源车标签页初始化
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加基础样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 20px;
        }
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    const cancelBtn = modal.querySelector('.cancel-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
