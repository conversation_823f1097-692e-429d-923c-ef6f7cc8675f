// 仓库管理页面JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签页切换
    initTabSwitching();
    
    // 初始化图表
    initCharts();
    
    // 初始化按钮事件
    initButtonEvents();
    
    // 初始化搜索和筛选
    initSearchAndFilter();
    
    // 初始化表格交互
    initTableInteractions();
    
    // 初始化实时数据更新
    initRealTimeUpdates();
});

// 标签页切换功能
function initTabSwitching() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // 添加当前活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // 根据标签页初始化相应功能
            switch(targetTab) {
                case 'inventory':
                    refreshInventoryData();
                    break;
                case 'tracking':
                    initTrackingSearch();
                    break;
                case 'warehouses':
                    loadWarehouseData();
                    break;
                case 'approval':
                    loadApprovalData();
                    break;
                case 'analysis':
                    loadAnalysisData();
                    break;
            }
        });
    });
}

// 初始化图表
function initCharts() {
    // 库存趋势图表
    const inventoryTrendCtx = document.getElementById('inventoryTrendChart');
    if (inventoryTrendCtx) {
        new Chart(inventoryTrendCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '库存总量',
                    data: [2800, 2650, 2900, 2750, 2856, 2920],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    }
                }
            }
        });
    }

    // 库存分布图表
    const inventoryDistributionCtx = document.getElementById('inventoryDistributionChart');
    if (inventoryDistributionCtx) {
        new Chart(inventoryDistributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['办公用品', 'IT设备', '清洁用品', '维修备件', '其他'],
                datasets: [{
                    data: [35, 25, 20, 15, 5],
                    backgroundColor: [
                        '#667eea',
                        '#f39c12',
                        '#27ae60',
                        '#e74c3c',
                        '#95a5a6'
                    ],
                    borderWidth: 0,
                    cutout: '60%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
    }

    // 周转率趋势图表
    const turnoverTrendCtx = document.getElementById('turnoverTrendChart');
    if (turnoverTrendCtx) {
        new Chart(turnoverTrendCtx, {
            type: 'bar',
            data: {
                labels: ['8月', '9月', '10月', '11月', '12月', '1月'],
                datasets: [{
                    label: '周转率',
                    data: [3.8, 4.1, 3.9, 4.3, 4.0, 4.2],
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: '#667eea',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 物品周转率排行图表
    const itemTurnoverCtx = document.getElementById('itemTurnoverChart');
    if (itemTurnoverCtx) {
        new Chart(itemTurnoverCtx, {
            type: 'horizontalBar',
            data: {
                labels: ['A4打印纸', '办公椅', '笔记本电脑', '清洁用品', '维修配件'],
                datasets: [{
                    label: '周转率',
                    data: [8.5, 3.2, 2.8, 1.2, 2.1],
                    backgroundColor: [
                        '#27ae60',
                        '#667eea',
                        '#f39c12',
                        '#e74c3c',
                        '#95a5a6'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }
}

// 初始化按钮事件
function initButtonEvents() {
    // 新建领用申请
    const newRequestBtn = document.getElementById('newRequestBtn');
    if (newRequestBtn) {
        newRequestBtn.addEventListener('click', function() {
            showRequestForm();
        });
    }

    // 库存盘点
    const inventoryBtn = document.getElementById('inventoryBtn');
    if (inventoryBtn) {
        inventoryBtn.addEventListener('click', function() {
            showInventoryForm();
        });
    }

    // 生成报表
    const reportBtn = document.getElementById('reportBtn');
    if (reportBtn) {
        reportBtn.addEventListener('click', function() {
            generateReport();
        });
    }

    // 新建调拨
    const newTransferBtn = document.getElementById('newTransferBtn');
    if (newTransferBtn) {
        newTransferBtn.addEventListener('click', function() {
            showTransferForm();
        });
    }

    // 预警处理按钮
    const alertBtns = document.querySelectorAll('.alert-actions .btn');
    alertBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const itemName = this.closest('.alert-item').querySelector('h4').textContent;
            handleAlertAction(action, itemName);
        });
    });

    // 建议执行按钮
    const suggestionBtns = document.querySelectorAll('.suggestion-item .btn, .recommendation-item .btn');
    suggestionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const suggestion = this.closest('.suggestion-item, .recommendation-item').querySelector('h4').textContent;
            handleSuggestion(action, suggestion);
        });
    });
}

// 显示领用申请表单
function showRequestForm() {
    const modal = createModal('新建领用申请', `
        <form class="request-form">
            <div class="form-row">
                <div class="form-group">
                    <label>申请部门 *</label>
                    <input type="text" name="department" required placeholder="请输入申请部门">
                </div>
                <div class="form-group">
                    <label>申请人 *</label>
                    <input type="text" name="applicant" required placeholder="请输入申请人">
                </div>
            </div>
            <div class="form-group">
                <label>物品名称 *</label>
                <input type="text" name="itemName" required placeholder="请输入物品名称">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>申请数量 *</label>
                    <input type="number" name="quantity" required placeholder="请输入申请数量">
                </div>
                <div class="form-group">
                    <label>紧急程度</label>
                    <select name="urgency">
                        <option value="normal">一般</option>
                        <option value="urgent">紧急</option>
                        <option value="very-urgent">非常紧急</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>使用用途 *</label>
                <textarea name="purpose" required placeholder="请详细说明使用用途"></textarea>
            </div>
            <div class="form-group">
                <label>预计使用时间</label>
                <input type="date" name="expectedDate">
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交申请</button>
            </div>
        </form>
    `);

    modal.querySelector('.request-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('领用申请已提交，等待审批', 'success');
        closeModal(modal);
    });
}

// 显示库存盘点表单
function showInventoryForm() {
    const modal = createModal('库存盘点', `
        <form class="inventory-form">
            <div class="form-group">
                <label>盘点仓库 *</label>
                <select name="warehouse" required>
                    <option value="">请选择仓库</option>
                    <option value="main">主仓库</option>
                    <option value="office">办公用品仓</option>
                    <option value="maintenance">维修备件仓</option>
                </select>
            </div>
            <div class="form-group">
                <label>盘点类型 *</label>
                <select name="type" required>
                    <option value="">请选择盘点类型</option>
                    <option value="full">全盘</option>
                    <option value="partial">抽盘</option>
                    <option value="cycle">循环盘点</option>
                </select>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>盘点日期 *</label>
                    <input type="date" name="date" required>
                </div>
                <div class="form-group">
                    <label>盘点人员 *</label>
                    <input type="text" name="staff" required placeholder="请输入盘点人员">
                </div>
            </div>
            <div class="form-group">
                <label>备注</label>
                <textarea name="notes" placeholder="请输入备注信息"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">开始盘点</button>
            </div>
        </form>
    `);

    modal.querySelector('.inventory-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('盘点任务已创建', 'success');
        closeModal(modal);
    });
}

// 显示调拨表单
function showTransferForm() {
    const modal = createModal('新建库存调拨', `
        <form class="transfer-form">
            <div class="form-row">
                <div class="form-group">
                    <label>调出仓库 *</label>
                    <select name="fromWarehouse" required>
                        <option value="">请选择调出仓库</option>
                        <option value="main">主仓库</option>
                        <option value="office">办公用品仓</option>
                        <option value="maintenance">维修备件仓</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>调入仓库 *</label>
                    <select name="toWarehouse" required>
                        <option value="">请选择调入仓库</option>
                        <option value="main">主仓库</option>
                        <option value="office">办公用品仓</option>
                        <option value="maintenance">维修备件仓</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>物品名称 *</label>
                <input type="text" name="itemName" required placeholder="请输入物品名称">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>调拨数量 *</label>
                    <input type="number" name="quantity" required placeholder="请输入调拨数量">
                </div>
                <div class="form-group">
                    <label>预计完成时间</label>
                    <input type="datetime-local" name="expectedTime">
                </div>
            </div>
            <div class="form-group">
                <label>调拨原因 *</label>
                <textarea name="reason" required placeholder="请说明调拨原因"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary cancel-btn">取消</button>
                <button type="submit" class="btn btn-primary">提交调拨</button>
            </div>
        </form>
    `);

    modal.querySelector('.transfer-form').addEventListener('submit', function(e) {
        e.preventDefault();
        Utils.showMessage('调拨申请已提交', 'success');
        closeModal(modal);
    });
}

// 生成报表
function generateReport() {
    Utils.showMessage('正在生成仓库管理报表...', 'info');
    // 模拟报表生成过程
    setTimeout(() => {
        Utils.showMessage('报表生成成功，已下载到本地', 'success');
    }, 2000);
}

// 处理预警操作
function handleAlertAction(action, itemName) {
    switch(action) {
        case '立即补货':
            Utils.showMessage(`正在为${itemName}创建补货申请...`, 'info');
            break;
        case '计划补货':
            Utils.showMessage(`已将${itemName}加入补货计划`, 'success');
            break;
        case '清仓处理':
            Utils.showMessage(`正在为${itemName}制定清仓方案...`, 'info');
            break;
        case '查看详情':
            Utils.showMessage(`正在查看${itemName}详细信息...`, 'info');
            break;
    }
}

// 处理建议
function handleSuggestion(action, suggestion) {
    switch(action) {
        case '执行建议':
        case '立即处理':
            Utils.showMessage(`正在执行${suggestion}...`, 'info');
            break;
        case '查看方案':
        case '查看详情':
            Utils.showMessage(`正在查看${suggestion}详细方案...`, 'info');
            break;
    }
}

// 初始化搜索和筛选
function initSearchAndFilter() {
    const searchInputs = document.querySelectorAll('.search-input');
    const filterSelects = document.querySelectorAll('.filter-select');

    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const keyword = this.value.toLowerCase();
            performSearch(keyword);
        });
    });

    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            const filterValue = this.value;
            applyFilter(filterValue);
        });
    });
}

// 执行搜索
function performSearch(keyword) {
    // 根据当前活动标签页执行相应的搜索
    const activeTab = document.querySelector('.tab-btn.active').dataset.tab;
    
    switch(activeTab) {
        case 'tracking':
            searchTracking(keyword);
            break;
        case 'approval':
            searchApproval(keyword);
            break;
    }
}

// 搜索追溯记录
function searchTracking(keyword) {
    // 模拟搜索追溯记录
    console.log('搜索追溯记录:', keyword);
}

// 搜索审批记录
function searchApproval(keyword) {
    const tableRows = document.querySelectorAll('.approval-table tbody tr');
    
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(keyword)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 应用筛选
function applyFilter(filterValue) {
    // 根据筛选值应用相应的筛选逻辑
    console.log('应用筛选:', filterValue);
}

// 初始化表格交互
function initTableInteractions() {
    const tableRows = document.querySelectorAll('.approval-table tbody tr');
    
    tableRows.forEach(row => {
        row.addEventListener('click', function() {
            // 高亮选中行
            tableRows.forEach(r => r.classList.remove('selected'));
            this.classList.add('selected');
        });
    });
}

// 初始化实时数据更新
function initRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(updateWarehouseData, 30000);
}

// 更新仓库数据
function updateWarehouseData() {
    // 模拟数据更新
    const cards = document.querySelectorAll('.overview-card');
    
    cards.forEach(card => {
        const number = card.querySelector('.card-number');
        if (number && !isNaN(parseInt(number.textContent))) {
            // 随机变化数据
            const currentValue = parseInt(number.textContent);
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, 1
            const newValue = Math.max(0, currentValue + change);
            
            if (newValue !== currentValue) {
                number.textContent = newValue;
                
                // 添加更新动画
                number.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    number.style.transform = '';
                }, 200);
            }
        }
    });
}

// 刷新库存数据
function refreshInventoryData() {
    Utils.showMessage('正在刷新库存数据...', 'info');
}

// 初始化追溯搜索
function initTrackingSearch() {
    const searchBtn = document.querySelector('.search-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            const inputs = document.querySelectorAll('.search-input');
            const values = Array.from(inputs).map(input => input.value);
            
            if (values.some(value => value.trim())) {
                Utils.showMessage('正在查询追溯信息...', 'info');
                // 这里可以添加实际的查询逻辑
            } else {
                Utils.showMessage('请输入查询条件', 'warning');
            }
        });
    }
}

// 加载仓库数据
function loadWarehouseData() {
    // 模拟加载仓库数据
}

// 加载审批数据
function loadApprovalData() {
    // 模拟加载审批数据
}

// 加载分析数据
function loadAnalysisData() {
    // 模拟加载分析数据
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-body {
            padding: 20px;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
        }
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 关闭事件
    const closeModal = () => {
        modal.remove();
        style.remove();
    };

    modal.querySelector('.close-btn').addEventListener('click', closeModal);
    modal.querySelector('.cancel-btn').addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    return modal;
}

// 关闭模态框
function closeModal(modal) {
    modal.remove();
}
