<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库管理 - 智慧后勤管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/warehouse.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="top-header">
        <div class="header-left">
            <button class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
            <h1 class="system-title">
                <i class="fas fa-building"></i>
                智慧后勤管理系统
            </h1>
        </div>
        <div class="header-right">
            <div class="notification-center">
                <button class="notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">8</span>
                </button>
            </div>
            <div class="user-profile">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="用户头像" class="user-avatar">
                <span class="user-name">张管理员</span>
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </header>

    <!-- 侧边导航栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-content">
            <!-- 用户信息卡片 -->
            <div class="user-card">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="用户头像" class="user-card-avatar">
                <div class="user-card-info">
                    <h3>张管理员</h3>
                    <p>系统管理员</p>
                </div>
            </div>

            <!-- 导航菜单 -->
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="procurement.html" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>
                        <span>采购管理</span>
                    </a>
                </li>
                
                <li class="nav-item active">
                    <a href="warehouse.html" class="nav-link">
                        <i class="fas fa-warehouse"></i>
                        <span>仓库管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="maintenance.html" class="nav-link">
                        <i class="fas fa-tools"></i>
                        <span>报修管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="engineering.html" class="nav-link">
                        <i class="fas fa-hard-hat"></i>
                        <span>工程管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="utilities.html" class="nav-link">
                        <i class="fas fa-bolt"></i>
                        <span>水电气管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="safety.html" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>安全生产管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="medical-waste.html" class="nav-link">
                        <i class="fas fa-biohazard"></i>
                        <span>医废管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="vehicle.html" class="nav-link">
                        <i class="fas fa-car"></i>
                        <span>用车管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="dormitory.html" class="nav-link">
                        <i class="fas fa-bed"></i>
                        <span>宿舍管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="cleaning.html" class="nav-link">
                        <i class="fas fa-broom"></i>
                        <span>保洁管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="inspection.html" class="nav-link">
                        <i class="fas fa-search"></i>
                        <span>巡检管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="contract.html" class="nav-link">
                        <i class="fas fa-file-contract"></i>
                        <span>合同管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="information.html" class="nav-link">
                        <i class="fas fa-bullhorn"></i>
                        <span>信息发布管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="property.html" class="nav-link">
                        <i class="fas fa-building"></i>
                        <span>物业管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="finance.html" class="nav-link">
                        <i class="fas fa-credit-card"></i>
                        <span>财务管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="catering.html" class="nav-link">
                        <i class="fas fa-utensils"></i>
                        <span>餐饮管理</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                <h2>
                    <i class="fas fa-warehouse"></i>
                    仓库管理
                </h2>
                <p>智能库存预警系统，物资全流程追溯，多仓库联动管理</p>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" id="newRequestBtn">
                    <i class="fas fa-plus"></i>
                    新建领用申请
                </button>
                <button class="btn btn-secondary" id="inventoryBtn">
                    <i class="fas fa-clipboard-list"></i>
                    库存盘点
                </button>
                <button class="btn btn-secondary" id="reportBtn">
                    <i class="fas fa-chart-bar"></i>
                    生成报表
                </button>
            </div>
        </div>

        <!-- 仓库概览 -->
        <section class="warehouse-overview">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="card-content">
                        <h3>库存预警</h3>
                        <div class="card-number">15</div>
                        <div class="card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+3项</span>
                        </div>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="card-content">
                        <h3>总库存量</h3>
                        <div class="card-number">2,856</div>
                        <div class="card-trend stable">
                            <i class="fas fa-minus"></i>
                            <span>0%</span>
                        </div>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-hand-paper"></i>
                    </div>
                    <div class="card-content">
                        <h3>待审批申请</h3>
                        <div class="card-number">8</div>
                        <div class="card-trend down">
                            <i class="fas fa-arrow-down"></i>
                            <span>-2项</span>
                        </div>
                    </div>
                </div>

                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="card-content">
                        <h3>库存周转率</h3>
                        <div class="card-number">4.2</div>
                        <div class="card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+0.3</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能模块导航 -->
        <section class="module-navigation">
            <div class="module-tabs">
                <button class="tab-btn active" data-tab="inventory">
                    <i class="fas fa-chart-line"></i>
                    智能库存预警
                </button>
                <button class="tab-btn" data-tab="tracking">
                    <i class="fas fa-route"></i>
                    物资追溯
                </button>
                <button class="tab-btn" data-tab="warehouses">
                    <i class="fas fa-building"></i>
                    多仓库管理
                </button>
                <button class="tab-btn" data-tab="approval">
                    <i class="fas fa-clipboard-check"></i>
                    领用审批
                </button>
                <button class="tab-btn" data-tab="analysis">
                    <i class="fas fa-analytics"></i>
                    周转分析
                </button>
            </div>
        </section>

        <!-- 智能库存预警模块 -->
        <section class="tab-content active" id="inventory">
            <div class="inventory-alerts">
                <div class="alerts-header">
                    <h3>
                        <i class="fas fa-exclamation-triangle"></i>
                        库存预警
                    </h3>
                    <div class="alert-filters">
                        <select class="filter-select">
                            <option>全部预警</option>
                            <option>库存不足</option>
                            <option>库存过剩</option>
                            <option>即将过期</option>
                        </select>
                        <button class="btn btn-sm btn-primary">
                            <i class="fas fa-sync"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <div class="alerts-list">
                    <div class="alert-item critical">
                        <div class="alert-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="alert-content">
                            <h4>A4打印纸</h4>
                            <p>当前库存: 50包 | 最低库存: 100包</p>
                            <div class="alert-meta">
                                <span class="warehouse">仓库: 主仓库</span>
                                <span class="time">2小时前</span>
                            </div>
                        </div>
                        <div class="alert-actions">
                            <button class="btn btn-sm btn-primary">立即补货</button>
                            <button class="btn btn-sm btn-secondary">查看详情</button>
                        </div>
                    </div>

                    <div class="alert-item warning">
                        <div class="alert-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="alert-content">
                            <h4>办公椅</h4>
                            <p>当前库存: 15把 | 最低库存: 10把</p>
                            <div class="alert-meta">
                                <span class="warehouse">仓库: 办公用品仓</span>
                                <span class="time">5小时前</span>
                            </div>
                        </div>
                        <div class="alert-actions">
                            <button class="btn btn-sm btn-warning">计划补货</button>
                            <button class="btn btn-sm btn-secondary">查看详情</button>
                        </div>
                    </div>

                    <div class="alert-item excess">
                        <div class="alert-icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="alert-content">
                            <h4>清洁用品</h4>
                            <p>当前库存: 500件 | 最高库存: 300件</p>
                            <div class="alert-meta">
                                <span class="warehouse">仓库: 清洁用品仓</span>
                                <span class="time">1天前</span>
                            </div>
                        </div>
                        <div class="alert-actions">
                            <button class="btn btn-sm btn-info">清仓处理</button>
                            <button class="btn btn-sm btn-secondary">查看详情</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="inventory-charts">
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>库存趋势分析</h3>
                            <select class="chart-select">
                                <option>近7天</option>
                                <option>近30天</option>
                                <option>近3个月</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="inventoryTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>库存分布</h3>
                            <select class="chart-select">
                                <option>按类别</option>
                                <option>按仓库</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="inventoryDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="inventory-suggestions">
                <div class="suggestions-header">
                    <h3>
                        <i class="fas fa-lightbulb"></i>
                        智能建议
                    </h3>
                </div>
                <div class="suggestions-list">
                    <div class="suggestion-item">
                        <div class="suggestion-icon restock">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="suggestion-content">
                            <h4>补货建议</h4>
                            <p>建议立即补充A4打印纸200包，预计3天内用完现有库存</p>
                            <div class="suggestion-priority high">高优先级</div>
                        </div>
                        <button class="btn btn-sm btn-primary">执行建议</button>
                    </div>

                    <div class="suggestion-item">
                        <div class="suggestion-icon clearance">
                            <i class="fas fa-minus-circle"></i>
                        </div>
                        <div class="suggestion-content">
                            <h4>清仓建议</h4>
                            <p>清洁用品库存过剩，建议促销或调拨至其他仓库</p>
                            <div class="suggestion-priority medium">中优先级</div>
                        </div>
                        <button class="btn btn-sm btn-info">查看方案</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 物资追溯模块 -->
        <section class="tab-content" id="tracking">
            <div class="tracking-search">
                <div class="search-header">
                    <h3>
                        <i class="fas fa-search"></i>
                        物资追溯查询
                    </h3>
                </div>
                <div class="search-form">
                    <div class="search-row">
                        <div class="search-group">
                            <label>追溯码</label>
                            <input type="text" placeholder="请输入物资追溯码" class="search-input">
                        </div>
                        <div class="search-group">
                            <label>物资名称</label>
                            <input type="text" placeholder="请输入物资名称" class="search-input">
                        </div>
                        <div class="search-group">
                            <label>时间范围</label>
                            <input type="date" class="search-input">
                        </div>
                        <div class="search-group">
                            <button class="btn btn-primary search-btn">
                                <i class="fas fa-search"></i>
                                查询
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tracking-result">
                <div class="result-header">
                    <h4>追溯结果: 戴尔笔记本电脑 (追溯码: WH2024001)</h4>
                    <button class="btn btn-sm btn-secondary">
                        <i class="fas fa-print"></i>
                        打印报告
                    </button>
                </div>

                <div class="tracking-timeline">
                    <div class="timeline-item">
                        <div class="timeline-date">2024-01-10</div>
                        <div class="timeline-icon purchase">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="timeline-content">
                            <h5>采购入库</h5>
                            <p>供应商: 戴尔(中国)有限公司</p>
                            <p>采购单号: PO2024001 | 数量: 1台</p>
                            <p>入库人员: 李仓管</p>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-date">2024-01-15</div>
                        <div class="timeline-icon storage">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="timeline-content">
                            <h5>仓库存储</h5>
                            <p>存储位置: A区-02-15</p>
                            <p>质检状态: 合格</p>
                            <p>保管员: 王保管</p>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-date">2024-01-20</div>
                        <div class="timeline-icon request">
                            <i class="fas fa-hand-paper"></i>
                        </div>
                        <div class="timeline-content">
                            <h5>领用申请</h5>
                            <p>申请部门: 技术部</p>
                            <p>申请人: 张工程师</p>
                            <p>申请理由: 开发工作需要</p>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-date">2024-01-21</div>
                        <div class="timeline-icon approval">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="timeline-content">
                            <h5>审批通过</h5>
                            <p>审批人: 刘主管</p>
                            <p>审批意见: 同意领用</p>
                        </div>
                    </div>

                    <div class="timeline-item current">
                        <div class="timeline-date">2024-01-22</div>
                        <div class="timeline-icon delivery">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="timeline-content">
                            <h5>物资发放</h5>
                            <p>领用人: 张工程师</p>
                            <p>发放人员: 李仓管</p>
                            <p>当前状态: 使用中</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 多仓库管理模块 -->
        <section class="tab-content" id="warehouses">
            <div class="warehouses-overview">
                <div class="warehouses-grid">
                    <div class="warehouse-card">
                        <div class="warehouse-header">
                            <div class="warehouse-icon">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div class="warehouse-info">
                                <h4>主仓库</h4>
                                <p>总部大楼B1层</p>
                            </div>
                            <div class="warehouse-status active">运行中</div>
                        </div>
                        <div class="warehouse-stats">
                            <div class="stat-row">
                                <span class="stat-label">总容量</span>
                                <span class="stat-value">1000㎡</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">使用率</span>
                                <span class="stat-value">78%</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">物品种类</span>
                                <span class="stat-value">156种</span>
                            </div>
                        </div>
                        <div class="warehouse-actions">
                            <button class="btn btn-sm btn-primary">查看详情</button>
                            <button class="btn btn-sm btn-secondary">库存调拨</button>
                        </div>
                    </div>

                    <div class="warehouse-card">
                        <div class="warehouse-header">
                            <div class="warehouse-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="warehouse-info">
                                <h4>办公用品仓</h4>
                                <p>办公楼3层东侧</p>
                            </div>
                            <div class="warehouse-status active">运行中</div>
                        </div>
                        <div class="warehouse-stats">
                            <div class="stat-row">
                                <span class="stat-label">总容量</span>
                                <span class="stat-value">300㎡</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">使用率</span>
                                <span class="stat-value">65%</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">物品种类</span>
                                <span class="stat-value">89种</span>
                            </div>
                        </div>
                        <div class="warehouse-actions">
                            <button class="btn btn-sm btn-primary">查看详情</button>
                            <button class="btn btn-sm btn-secondary">库存调拨</button>
                        </div>
                    </div>

                    <div class="warehouse-card">
                        <div class="warehouse-header">
                            <div class="warehouse-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="warehouse-info">
                                <h4>维修备件仓</h4>
                                <p>设备间附属仓库</p>
                            </div>
                            <div class="warehouse-status maintenance">维护中</div>
                        </div>
                        <div class="warehouse-stats">
                            <div class="stat-row">
                                <span class="stat-label">总容量</span>
                                <span class="stat-value">200㎡</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">使用率</span>
                                <span class="stat-value">45%</span>
                            </div>
                            <div class="stat-row">
                                <span class="stat-label">物品种类</span>
                                <span class="stat-value">67种</span>
                            </div>
                        </div>
                        <div class="warehouse-actions">
                            <button class="btn btn-sm btn-secondary">查看详情</button>
                            <button class="btn btn-sm btn-warning">维护计划</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="transfer-management">
                <div class="transfer-header">
                    <h3>
                        <i class="fas fa-exchange-alt"></i>
                        库存调拨管理
                    </h3>
                    <button class="btn btn-primary" id="newTransferBtn">
                        <i class="fas fa-plus"></i>
                        新建调拨
                    </button>
                </div>

                <div class="transfer-list">
                    <div class="transfer-item">
                        <div class="transfer-info">
                            <h4>A4打印纸调拨</h4>
                            <p>主仓库 → 办公用品仓 | 数量: 100包</p>
                            <div class="transfer-meta">
                                <span>申请人: 李仓管</span>
                                <span>申请时间: 2024-01-22 10:30</span>
                            </div>
                        </div>
                        <div class="transfer-status pending">待审批</div>
                        <div class="transfer-actions">
                            <button class="btn btn-sm btn-primary">审批</button>
                            <button class="btn btn-sm btn-secondary">查看</button>
                        </div>
                    </div>

                    <div class="transfer-item">
                        <div class="transfer-info">
                            <h4>办公椅调拨</h4>
                            <p>办公用品仓 → 主仓库 | 数量: 5把</p>
                            <div class="transfer-meta">
                                <span>申请人: 王保管</span>
                                <span>申请时间: 2024-01-21 14:20</span>
                            </div>
                        </div>
                        <div class="transfer-status approved">已完成</div>
                        <div class="transfer-actions">
                            <button class="btn btn-sm btn-secondary">查看</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 领用审批模块 -->
        <section class="tab-content" id="approval">
            <div class="approval-stats">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h4>待审批</h4>
                            <div class="stat-number">8</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon approved">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="stat-content">
                            <h4>已通过</h4>
                            <div class="stat-number">25</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon rejected">
                            <i class="fas fa-times"></i>
                        </div>
                        <div class="stat-content">
                            <h4>已拒绝</h4>
                            <div class="stat-number">2</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="approval-list">
                <div class="list-header">
                    <h3>
                        <i class="fas fa-clipboard-check"></i>
                        领用申请列表
                    </h3>
                    <div class="list-filters">
                        <select class="filter-select">
                            <option>全部状态</option>
                            <option>待审批</option>
                            <option>已通过</option>
                            <option>已拒绝</option>
                        </select>
                        <input type="text" placeholder="搜索申请人或物品..." class="search-input">
                    </div>
                </div>

                <div class="approval-table">
                    <table>
                        <thead>
                            <tr>
                                <th>申请编号</th>
                                <th>申请人</th>
                                <th>部门</th>
                                <th>物品名称</th>
                                <th>数量</th>
                                <th>申请时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>REQ2024001</td>
                                <td>张工程师</td>
                                <td>技术部</td>
                                <td>笔记本电脑</td>
                                <td>1台</td>
                                <td>2024-01-22 09:30</td>
                                <td><span class="status pending">待审批</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">审批</button>
                                    <button class="btn btn-sm btn-secondary">查看</button>
                                </td>
                            </tr>
                            <tr>
                                <td>REQ2024002</td>
                                <td>李秘书</td>
                                <td>行政部</td>
                                <td>A4打印纸</td>
                                <td>20包</td>
                                <td>2024-01-22 10:15</td>
                                <td><span class="status pending">待审批</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary">审批</button>
                                    <button class="btn btn-sm btn-secondary">查看</button>
                                </td>
                            </tr>
                            <tr>
                                <td>REQ2024003</td>
                                <td>王主管</td>
                                <td>财务部</td>
                                <td>办公椅</td>
                                <td>2把</td>
                                <td>2024-01-21 16:20</td>
                                <td><span class="status approved">已通过</span></td>
                                <td>
                                    <button class="btn btn-sm btn-secondary">查看</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- 周转分析模块 -->
        <section class="tab-content" id="analysis">
            <div class="analysis-overview">
                <div class="kpi-cards">
                    <div class="kpi-card">
                        <div class="kpi-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="kpi-content">
                            <h4>平均周转率</h4>
                            <div class="kpi-value">4.2</div>
                            <div class="kpi-trend up">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0.3</span>
                            </div>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="kpi-content">
                            <h4>平均库存天数</h4>
                            <div class="kpi-value">87天</div>
                            <div class="kpi-trend down">
                                <i class="fas fa-arrow-down"></i>
                                <span>-5天</span>
                            </div>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="kpi-content">
                            <h4>库存利用率</h4>
                            <div class="kpi-value">73%</div>
                            <div class="kpi-trend up">
                                <i class="fas fa-arrow-up"></i>
                                <span>+5%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="analysis-charts">
                <div class="chart-row">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>库存周转率趋势</h3>
                            <select class="chart-select">
                                <option>近6个月</option>
                                <option>近1年</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="turnoverTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>物品周转率排行</h3>
                            <select class="chart-select">
                                <option>本月</option>
                                <option>本季度</option>
                            </select>
                        </div>
                        <div class="chart-container">
                            <canvas id="itemTurnoverChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="analysis-recommendations">
                <div class="recommendations-header">
                    <h3>
                        <i class="fas fa-lightbulb"></i>
                        优化建议
                    </h3>
                </div>
                <div class="recommendations-list">
                    <div class="recommendation-item">
                        <div class="recommendation-icon slow">
                            <i class="fas fa-turtle"></i>
                        </div>
                        <div class="recommendation-content">
                            <h4>滞销物品处理</h4>
                            <p>清洁用品周转率过低(1.2)，建议减少采购量或促销处理</p>
                            <div class="recommendation-priority medium">中优先级</div>
                        </div>
                        <button class="btn btn-sm btn-warning">查看详情</button>
                    </div>

                    <div class="recommendation-item">
                        <div class="recommendation-icon fast">
                            <i class="fas fa-rabbit"></i>
                        </div>
                        <div class="recommendation-content">
                            <h4>热销物品补货</h4>
                            <p>A4打印纸周转率很高(8.5)，建议增加库存量</p>
                            <div class="recommendation-priority high">高优先级</div>
                        </div>
                        <button class="btn btn-sm btn-primary">立即处理</button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="js/common.js"></script>
    <script src="js/warehouse.js"></script>
</body>
</html>
